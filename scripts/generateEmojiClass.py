import asyncio
import os

import aiofiles
import discord
from dotenv import load_dotenv

load_dotenv()
TOKEN = os.getenv('DISCORD_TOKEN')


class EmojiDumpBot(discord.Client):
    async def on_ready(self):
        emojis = await self.fetch_application_emojis()
        print(f'Logged in as {self.user}')
        async with aiofiles.open(
            'utils/modules/emojis/emojisStatic.py', 'w', encoding='utf-8'
        ) as f:
            print('Generating emojisStatic.py...')
            await f.write('import discord\n\n')
            await f.write('class Emojis:\n')
            for emoji in emojis:
                safe_name = emoji.name.replace(' ', '_').replace('-', '_')
                await f.write(f'    {safe_name}: discord.Emoji\n')

        print(f'Generated {len(emojis)} emojis in emojisStatic.py')
        await self.close()


async def main():
    intents = discord.Intents(guilds=True)
    async with EmojiDumpBot(intents=intents) as bot:
        if not TOKEN:
            raise ValueError('TOKEN environment variable not set')
        await bot.start(TOKEN)


asyncio.run(main())
