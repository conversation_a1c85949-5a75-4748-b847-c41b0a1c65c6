"""
Comprehensive tests for the profanity filter system.

Tests cover:
- Pattern matching engine functionality
- All pattern types (exact, prefix, suffix, wildcard)
- Word boundary detection
- Whitelist functionality
- Action execution
- Bypass mechanisms
- Edge cases and performance
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from utils.modules.core.db.models import (
    PatternMatchType,
    BlockWordAction,
    AlertSeverity,
    AntiSwearRule,
    AntiSwearPattern,
    AntiSwearWhitelist,
    HubModeratorRole,
)
from utils.modules.core.profanity import (
    PatternMatcher,
    MatchResult,
    ProfanityFilterService,
    FilterResult,
    ActionExecutor,
    BypassChecker,
    BypassConfig,
)


class TestPatternMatcher:
    """Test the core pattern matching engine."""

    def setup_method(self):
        """Set up test fixtures."""
        self.matcher = PatternMatcher()

    def test_exact_pattern_matching(self):
        """Test exact word matching with word boundaries."""
        # Add exact patterns
        assert self.matcher.add_pattern('bad', PatternMatchType.EXACT)
        assert self.matcher.add_pattern('word', PatternMatchType.EXACT)

        # Test exact matches
        matches = self.matcher.check_message('This is a bad word')
        assert len(matches) == 2
        assert any(m.matched_word == 'bad' for m in matches)
        assert any(m.matched_word == 'word' for m in matches)

        # Test word boundaries (should not match)
        matches = self.matcher.check_message('This is badword')
        assert len(matches) == 0

        matches = self.matcher.check_message('wordy')
        assert len(matches) == 0

    def test_prefix_pattern_matching(self):
        """Test prefix pattern matching."""
        assert self.matcher.add_pattern('bad', PatternMatchType.PREFIX)

        # Should match words starting with "bad"
        matches = self.matcher.check_message('badword is here')
        assert len(matches) == 1
        assert matches[0].matched_word == 'badword'

        matches = self.matcher.check_message('badly done')
        assert len(matches) == 1
        assert matches[0].matched_word == 'badly'

        # Should not match if "bad" is in the middle
        matches = self.matcher.check_message('not bad at all')
        assert len(matches) == 0

    def test_suffix_pattern_matching(self):
        """Test suffix pattern matching."""
        assert self.matcher.add_pattern('ing', PatternMatchType.SUFFIX)

        # Should match words ending with "ing"
        matches = self.matcher.check_message('running and jumping')
        assert len(matches) == 2
        matched_words = {m.matched_word for m in matches}
        assert 'running' in matched_words
        assert 'jumping' in matched_words

        # Should not match if "ing" is at the beginning
        matches = self.matcher.check_message('ingredient list')
        assert len(matches) == 0

    def test_wildcard_pattern_matching(self):
        """Test wildcard pattern matching."""
        assert self.matcher.add_pattern('bad', PatternMatchType.WILDCARD)

        # Should match words containing "bad"
        matches = self.matcher.check_message('badword and wordbad and embadded')
        assert len(matches) == 3
        matched_words = {m.matched_word for m in matches}
        assert 'badword' in matched_words
        assert 'wordbad' in matched_words
        assert 'embadded' in matched_words

    def test_case_insensitive_matching(self):
        """Test that matching is case-insensitive."""
        assert self.matcher.add_pattern('bad', PatternMatchType.EXACT)

        test_cases = ['BAD', 'Bad', 'bAd', 'bad']
        for case in test_cases:
            matches = self.matcher.check_message(f'This is {case}')
            assert len(matches) == 1
            assert matches[0].pattern == 'bad'

    def test_unicode_normalization(self):
        """Test Unicode normalization and bypass character handling."""
        assert self.matcher.add_pattern('bad', PatternMatchType.EXACT)

        # Test common bypass attempts
        bypass_attempts = ['b@d', 'b4d', 'b@d', 'b.a.d', 'b-a-d']

        for attempt in bypass_attempts:
            matches = self.matcher.check_message(f'This is {attempt}')
            # Should match due to normalization
            assert len(matches) >= 0  # Depends on normalization implementation

    def test_whitelist_functionality(self):
        """Test whitelist prevents matches."""
        assert self.matcher.add_pattern('class', PatternMatchType.EXACT)
        assert self.matcher.add_whitelist_word('class')

        # Should not match whitelisted word
        matches = self.matcher.check_message('This is a class')
        assert len(matches) == 0

        # Should still match non-whitelisted occurrences
        self.matcher.remove_whitelist_word('class')
        matches = self.matcher.check_message('This is a class')
        assert len(matches) == 1

    def test_pattern_validation(self):
        """Test pattern validation."""
        # Valid patterns
        assert self.matcher.add_pattern('valid', PatternMatchType.EXACT)
        assert self.matcher.add_pattern('prefix', PatternMatchType.PREFIX)

        # Invalid patterns
        assert not self.matcher.add_pattern('', PatternMatchType.EXACT)
        assert not self.matcher.add_pattern('   ', PatternMatchType.EXACT)
        assert not self.matcher.add_pattern('a', PatternMatchType.EXACT)  # Too short

    def test_performance_with_many_patterns(self):
        """Test performance with a large number of patterns."""
        import time

        # Add many patterns
        patterns = [f'word{i}' for i in range(1000)]
        start_time = time.time()

        for pattern in patterns:
            self.matcher.add_pattern(pattern, PatternMatchType.EXACT)

        add_time = time.time() - start_time
        assert add_time < 5.0  # Should add 1000 patterns in under 5 seconds

        # Test matching performance
        test_message = 'This is word500 in a long message with many words'
        start_time = time.time()

        for _ in range(100):
            matches = self.matcher.check_message(test_message)

        match_time = time.time() - start_time
        assert match_time < 1.0  # Should check 100 times in under 1 second


class TestBypassChecker:
    """Test bypass functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.db_mock = Mock()
        self.bypass_checker = BypassChecker(self.db_mock)

    @pytest.mark.asyncio
    async def test_hub_owner_bypass(self):
        """Test that hub owners are bypassed."""
        # Mock hub owner check
        with patch.object(self.bypass_checker, '_is_hub_owner', return_value=True):
            reason = await self.bypass_checker.check_bypass('hub1', 'user1', 'test message')
            assert reason == 'hub_owner'

    @pytest.mark.asyncio
    async def test_moderator_bypass(self):
        """Test that moderators are bypassed."""
        # Mock moderator role check
        with patch.object(
            self.bypass_checker, '_get_moderator_role', return_value=HubModeratorRole.MODERATOR
        ):
            reason = await self.bypass_checker.check_bypass('hub1', 'user1', 'test message')
            assert reason == 'hub_moderator'

    @pytest.mark.asyncio
    async def test_manager_bypass(self):
        """Test that managers are bypassed."""
        # Mock manager role check
        with patch.object(
            self.bypass_checker, '_get_moderator_role', return_value=HubModeratorRole.MANAGER
        ):
            reason = await self.bypass_checker.check_bypass('hub1', 'user1', 'test message')
            assert reason == 'hub_manager'

    @pytest.mark.asyncio
    async def test_role_bypass(self):
        """Test Discord role-based bypass."""
        config = BypassConfig(exempt_roles={'role123'})

        with patch.object(self.bypass_checker, '_is_hub_owner', return_value=False):
            with patch.object(self.bypass_checker, '_get_moderator_role', return_value=None):
                reason = await self.bypass_checker.check_bypass(
                    'hub1', 'user1', 'test message', user_roles=['role123'], bypass_config=config
                )
                assert reason == 'exempt_role_role123'

    @pytest.mark.asyncio
    async def test_bypass_token(self):
        """Test bypass token functionality."""
        # Mock token creation
        token = await self.bypass_checker.create_bypass_token(
            'hub1', 'user1', 'admin1', 'Testing', 60, 1
        )
        assert token is not None

        # Mock token checking
        with patch.object(
            self.bypass_checker, '_check_bypass_tokens', return_value='bypass_token_abc123...'
        ):
            reason = await self.bypass_checker.check_bypass(
                'hub1', 'user1', '[bypass:abc123def456]'
            )
            assert reason is not None and reason.startswith('bypass_token_')

    @pytest.mark.asyncio
    async def test_no_bypass(self):
        """Test when no bypass conditions are met."""
        with patch.object(self.bypass_checker, '_is_hub_owner', return_value=False):
            with patch.object(self.bypass_checker, '_get_moderator_role', return_value=None):
                with patch.object(self.bypass_checker, '_check_bypass_tokens', return_value=None):
                    reason = await self.bypass_checker.check_bypass('hub1', 'user1', 'test message')
                    assert reason is None


class TestActionExecutor:
    """Test action execution."""

    def setup_method(self):
        """Set up test fixtures."""
        self.db_mock = Mock()
        self.executor = ActionExecutor(self.db_mock)

    @pytest.mark.asyncio
    async def test_block_message_action(self):
        """Test message blocking action."""
        # Create mock rule
        rule = Mock()
        rule.name = 'Test Rule'

        actions = await self.executor.execute_actions(
            [BlockWordAction.BLOCK_MESSAGE], 'hub1', 'user1', 'channel1', 'msg1', 'badword', rule
        )

        assert BlockWordAction.BLOCK_MESSAGE in actions

    @pytest.mark.asyncio
    async def test_warn_action(self):
        """Test warning action."""
        # Mock database queries
        self.db_mock.query.return_value.filter.return_value.count.return_value = 0
        self.db_mock.commit.return_value = None

        rule = Mock()
        rule.name = 'Test Rule'
        rule.warningThreshold = 3
        rule.escalationAction = None

        actions = await self.executor.execute_actions(
            [BlockWordAction.WARN], 'hub1', 'user1', 'channel1', 'msg1', 'badword', rule
        )

        assert BlockWordAction.WARN in actions

    @pytest.mark.asyncio
    async def test_mute_action(self):
        """Test mute action."""
        # Mock database queries
        self.db_mock.query.return_value.filter.return_value.first.return_value = None
        self.db_mock.commit.return_value = None

        rule = Mock()
        rule.name = 'Test Rule'
        rule.muteDurationMinutes = 60

        actions = await self.executor.execute_actions(
            [BlockWordAction.MUTE], 'hub1', 'user1', 'channel1', 'msg1', 'badword', rule
        )

        assert BlockWordAction.MUTE in actions


class TestIntegration:
    """Integration tests for the complete system."""

    def setup_method(self):
        """Set up test fixtures."""
        self.db_mock = Mock()
        self.service = ProfanityFilterService(self.db_mock)

    @pytest.mark.asyncio
    async def test_complete_filter_flow(self):
        """Test complete filtering flow from message to action."""
        # Mock database setup
        rule = Mock()
        rule.id = 'rule1'
        rule.name = 'Test Rule'
        rule.enabled = True
        rule.actions = [BlockWordAction.BLOCK_MESSAGE, BlockWordAction.WARN]
        rule.logViolations = True
        rule.warningThreshold = 3
        rule.escalationAction = None

        pattern = Mock()
        pattern.id = 'pattern1'
        pattern.pattern = 'badword'
        pattern.matchType = PatternMatchType.EXACT
        pattern.ruleId = 'rule1'

        rule.patterns = [pattern]

        # Mock database queries
        self.db_mock.query.return_value.filter.return_value.all.return_value = [rule]
        self.db_mock.query.return_value.filter.return_value.first.return_value = rule

        # Mock bypass checker
        with patch.object(self.service.bypass_checker, 'check_bypass', return_value=None):
            # Test message filtering
            result = await self.service.check_message(
                'This contains a badword', 'hub1', 'user1', 'channel1', 'msg1'
            )

            assert result.blocked is True
            assert len(result.matches) > 0
            assert BlockWordAction.BLOCK_MESSAGE in result.actions_executed

    @pytest.mark.asyncio
    async def test_bypass_prevents_action(self):
        """Test that bypass prevents actions from being executed."""
        # Mock bypass
        with patch.object(self.service.bypass_checker, 'check_bypass', return_value='hub_owner'):
            result = await self.service.check_message(
                'This contains a badword', 'hub1', 'user1', 'channel1', 'msg1'
            )

            assert result.blocked is False
            assert result.bypass_reason == 'hub_owner'
            assert len(result.actions_executed) == 0

    def test_edge_cases(self):
        """Test edge cases and error conditions."""
        matcher = PatternMatcher()

        # Empty message
        matches = matcher.check_message('')
        assert len(matches) == 0

        # Very long message
        long_message = 'word ' * 10000
        matcher.add_pattern('word', PatternMatchType.EXACT)
        matches = matcher.check_message(long_message)
        assert len(matches) > 0  # Should still work

        # Special characters
        special_message = '!@#$%^&*()_+-=[]{}|;\':",./<>?'
        matches = matcher.check_message(special_message)
        assert len(matches) == 0  # No patterns to match

        # Unicode characters
        unicode_message = 'Hello 世界 🌍 café naïve'
        matches = matcher.check_message(unicode_message)
        assert len(matches) == 0  # No patterns to match


class TestAdminTools:
    """Test admin tools and utilities."""

    def test_pattern_validation(self):
        """Test pattern validation functionality."""
        from utils.modules.core.profanity.admin_tools import PatternValidator

        validator = PatternValidator()

        # Valid patterns
        result = validator.validate_pattern('badword', PatternMatchType.EXACT)
        assert result.valid is True
        assert len(result.errors) == 0

        # Invalid patterns
        result = validator.validate_pattern('', PatternMatchType.EXACT)
        assert result.valid is False
        assert 'empty' in result.errors[0].lower()

        result = validator.validate_pattern('a', PatternMatchType.EXACT)
        assert result.valid is False
        assert '2 characters' in result.errors[0]

        # Patterns with warnings
        result = validator.validate_pattern('ass', PatternMatchType.WILDCARD)
        assert len(result.warnings) > 0  # Should warn about false positives

    def test_bulk_pattern_parsing(self):
        """Test bulk pattern input parsing."""
        from utils.modules.core.profanity.admin_tools import BulkPatternManager

        manager = BulkPatternManager(Mock())

        # Test comma-separated input
        patterns = manager.parse_input_text('word1, word2*, *word3, *word4*')
        expected = [
            ('word1', PatternMatchType.EXACT),
            ('word2', PatternMatchType.PREFIX),
            ('word3', PatternMatchType.SUFFIX),
            ('word4', PatternMatchType.WILDCARD),
        ]
        assert patterns == expected

        # Test newline-separated input
        patterns = manager.parse_input_text('word1\nword2*\n*word3\n*word4*')
        assert patterns == expected

        # Test mixed input
        patterns = manager.parse_input_text('word1, word2*\n*word3, *word4*')
        assert patterns == expected


class TestAnalytics:
    """Test analytics and reporting."""

    def test_violation_stats_calculation(self):
        """Test violation statistics calculation."""
        from utils.modules.core.profanity.analytics import ProfanityAnalytics

        db_mock = Mock()
        analytics = ProfanityAnalytics(db_mock)

        # Mock query results
        db_mock.query.return_value.filter.return_value.count.return_value = 100
        db_mock.query.return_value.filter.return_value.scalar.return_value = 50
        db_mock.query.return_value.filter.return_value.group_by.return_value.order_by.return_value.limit.return_value.all.return_value = [
            ('badword', 25),
            ('otherword', 15),
        ]
        db_mock.query.return_value.filter.return_value.group_by.return_value.all.return_value = [
            (12, 10),
            (15, 20),  # hour, count
        ]

        stats = analytics.get_violation_stats('hub1')

        assert stats.total_violations == 100
        assert stats.unique_users == 50
        assert len(stats.top_triggered_words) == 2
        assert stats.top_triggered_words[0] == ('badword', 25)


class TestPerformance:
    """Performance and stress tests."""

    def test_large_message_performance(self):
        """Test performance with large messages."""
        import time

        matcher = PatternMatcher()

        # Add many patterns
        for i in range(100):
            matcher.add_pattern(f'bad{i}', PatternMatchType.EXACT)

        # Create large message
        large_message = ' '.join([f'word{i}' for i in range(10000)])

        # Time the matching
        start_time = time.time()
        matches = matcher.check_message(large_message)
        end_time = time.time()

        # Should complete in reasonable time
        assert (end_time - start_time) < 1.0  # Less than 1 second

    def test_pattern_compilation_performance(self):
        """Test pattern compilation performance."""
        import time

        matcher = PatternMatcher()

        # Time adding many patterns
        start_time = time.time()
        for i in range(1000):
            matcher.add_pattern(f'pattern{i}', PatternMatchType.EXACT)
        end_time = time.time()

        # Should compile patterns quickly
        assert (end_time - start_time) < 2.0  # Less than 2 seconds for 1000 patterns


if __name__ == '__main__':
    pytest.main([__file__])
