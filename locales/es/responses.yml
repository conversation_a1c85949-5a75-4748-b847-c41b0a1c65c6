responses:
  setup:
    setupComplete: "¿Listo para crear? ¡Haz clic en el botón Crear Hub!"
    editMessagePrompt: "{emoji} Usa el modal para editar tu mensaje."
    preview:
      titleSaved: "{emoji} ¡Información del hub guardada!"
      previewTitle: "Aquí tienes una vista previa de tu hub:"
      name: "{emoji} Nombre"
      short: "{emoji} Nombre Corto"
      description: "{emoji} Descripción"
    locale:
      successTitle: "¡Éxito!"
      successDescription: "{tick} Tu idioma se ha configurado a **{locale_name}**"
    loading:
      creatingHub: "{loading} Creando Tu Hub..."
      pleaseWait: "Por favor espera mientras configuramos tu espacio comunitario."
    errors:
      hubCreationFailed: "{no} Falló la Creación del Hub"
  appeal:
    constants:
      unknownHub: "Hub Desconocido"
      noReason: "Sin razón proporcionada"
    embed:
      title: "Tus Infracciones Apelables"
      description: "Selecciona una infracción abajo para enviar una apelación, o ver el estado de apelaciones existentes."
      noInfractions:
        title: "Sin Infracciones Activas"
        description: "No tienes infracciones activas que puedan ser apeladas."
      footer:
        canAppeal: "💡 Puedes apelar {count} infracción(es). Usa el menú de selección abajo."
        checkLater: "💡 Revisa más tarde cuando expiren los enfriamientos o se revisen las apelaciones."
  errors:
    interactionCheck: "No puedes usar esta interacción, ya que no la invocaste."
    rateLimited: "Estás siendo limitado por velocidad. Tómate un descanso."
    webhookRateLimit: "Has alcanzado el límite de velocidad de creación de webhooks"
    invalidInput: "No has proporcionado una entrada válida."
    invalidInvite: "Esta invitación es inválida o ha expirado."
    webhookError: "Falló al crear webhook."
    notConnected: "No pude encontrar un hub conectado en este canal."
    noInteraction: "Este comando solo soporta comandos de barra."
    missingAppealReference: "Falta referencia de apelación."
    errorTitle: "¡Error!"
    whoops: "¡Ups!"
    missingArgument: "Argumento faltante: `{param}`."
  welcome:
    onGuildJoinTitle: "👋 ¡Hola!"
    onGuildJoinDescription: |
      **Soy InterChat y me alegra estar en tu servidor en nombre de nuestro equipo.** Juntos podemos conectar tu servidor con otras comunidades increíbles en Discord. Miles de servidores en un solo lugar, llenos de personas con ganas de conversar. 🚀 **¿Listo para empezar?**
      {dot} **¿Eres nuevo?** El comando `/setup` te guiará paso a paso para comenzar.
      {dot} **¿Quieres explorar?** Visita nuestra [página de descubrimiento](https://interchat.tech/hubs) para encontrar y unirte a hubs activos.
      {dot} **¿Prefieres algo más directo?** Prueba `/call` para conexiones uno a uno.
      💝 **¿Necesitas ayuda?** Únete a nuestra [comunidad de soporte](https://discord.gg/8DhUA4HNpD). ¡Estaremos encantados de ayudarte!

