import json
from typing import TYPE_CHECKING, Literal, Optional
from dataclasses import dataclass
from enum import Enum

import discord
from discord.ext import commands
from sqlalchemy import select

from utils.constants import InterchatConstants
from utils.modules.core.db.models import (
    Hub,
    InfractionType,
)
from utils.modules.core.moderation import (
    fetch_original_msg_with_extra,
    get_user_moderated_hubs,
    mod_panel_embed,
)
from utils.modules.errors.customDiscord import NoInteraction
from utils.modules.events.hubLoggingHelpers import HubLogger
from utils.modules.services.moderationService import NO_REASON, ModerationService
from utils.modules.ui.AutoComplete import hubm_autocomplete
from utils.modules.ui.views.ModerationViews import HubSelectionView, ModPanelView
from utils.modules.hub.utils import get_user_permission
from utils.modules.hub.constants import HubPermissionLevel
from utils.modules.core.checks import is_interchat_staff_direct
from utils.modules.core.i18n import t

if TYPE_CHECKING:
    from main import Bot

SupportedModActions = Literal['warn', 'mute', 'ban', 'blacklist']


class ActionType(Enum):
    WARN = 'warn'
    MUTE = 'mute'
    BAN = 'ban'
    UNMUTE = 'unmute'
    UNBAN = 'unban'
    DELETE = 'delete'
    BLACKLIST = 'blacklist'


@dataclass
class ModerationTarget:
    """Represents a target for moderation action (user or server)."""

    user: Optional[discord.User | discord.Member] = None
    server: Optional[discord.Guild] = None

    @property
    def is_user(self) -> bool:
        return self.user is not None

    @property
    def is_server(self) -> bool:
        return self.server is not None

    @property
    def target_id(self) -> str:
        if self.user:
            return str(self.user.id)
        elif self.server:
            return str(self.server.id)
        return ''

    @property
    def target_name(self) -> str:
        if self.user:
            return str(self.user)
        elif self.server:
            return str(self.server)
        return ''

    def validate(self) -> tuple[bool, Optional[str]]:
        """Validate the target. Returns (is_valid, error_message)."""
        if self.user and self.server:
            return False, t('responses.moderation.target.both', locale='en')
        if not self.user and not self.server:
            return False, t('responses.moderation.target.missing', locale='en')
        return True, None


class ModerationActionHandler:
    def __init__(
        self,
        bot: 'Bot',
        moderator: discord.User | discord.Member,
        selected_hub: Hub,
        constants: InterchatConstants,
        locale: str,
    ):
        self.bot = bot
        self.moderator = moderator
        self.hub = selected_hub
        self.constants = constants
        self.locale = locale

    async def _ensure_permission(
        self,
        ctx: discord.Interaction | commands.Context,
        required_level: HubPermissionLevel = HubPermissionLevel.MODERATOR,
    ) -> bool:
        """Check if moderator has required permissions."""
        perm = get_user_permission(self.hub, str(self.moderator.id))
        if perm.value < required_level.value:
            permission_name = required_level.name.title()
            await self.send_message(
                ctx,
                f'{self.bot.emotes.x_icon} '
                + t(
                    'responses.moderation.permissions.insufficient',
                    locale=self.locale,
                    permission=permission_name,
                ),
                ephemeral=True,
            )
            return False
        return True

    async def _check_existing_infraction(
        self, modsvc: ModerationService, target: ModerationTarget, infraction_type: InfractionType
    ) -> bool:
        """Check if target already has active infraction of given type."""
        active = await modsvc.get_active_infractions(
            self.hub.id,
            user_id=target.target_id if target.is_user else None,
            server_id=target.target_id if target.is_server else None,
        )
        return any(inf.type == infraction_type for inf in active)

    async def _create_infraction(
        self,
        modsvc: ModerationService,
        target: ModerationTarget,
        infraction_type: InfractionType,
        reason: Optional[str] = None,
        duration_ms: Optional[int] = None,
    ):
        """Create a new infraction with proper error handling."""
        return await modsvc.create_infraction(
            hub_id=self.hub.id,
            mod_id=str(self.moderator.id),
            infraction_type=infraction_type,
            reason=reason or NO_REASON,
            duration_ms=duration_ms,
            user_id=target.target_id if target.is_user else None,
            server_id=target.target_id if target.is_server else None,
            server_name=target.target_name if target.is_server else None,
        )

    async def _log_action(
        self, action: ActionType, target: ModerationTarget, reason: str, expires_at=None
    ):
        """Log the moderation action."""
        base_kwargs = {
            'hub_id': self.hub.id,
            'hub_name': self.hub.name,
            'moderator_id': str(self.moderator.id),
            'moderator_name': str(self.moderator),
        }

        if target.is_user:
            base_kwargs.update(
                {
                    'user_id': target.target_id,
                    'user_name': target.target_name,
                }
            )

            log_methods = {
                ActionType.WARN: HubLogger.log_user_warn,
                ActionType.MUTE: HubLogger.log_user_mute,
                ActionType.BAN: HubLogger.log_user_ban,
                ActionType.UNMUTE: HubLogger.log_user_unmute,
                ActionType.UNBAN: HubLogger.log_user_unban,
            }
        else:
            base_kwargs.update(
                {
                    'server_id': target.target_id,
                    'server_name': target.target_name,
                }
            )

            log_methods = {
                ActionType.WARN: HubLogger.log_server_warn,
                ActionType.MUTE: HubLogger.log_server_mute,
                ActionType.BAN: HubLogger.log_server_ban,
                ActionType.UNMUTE: HubLogger.log_server_unmute,
                ActionType.UNBAN: HubLogger.log_server_unban,
            }

        log_method = log_methods.get(action)
        if log_method:
            if action in [ActionType.MUTE, ActionType.BAN]:
                base_kwargs['reason'] = reason
                if expires_at is not None:
                    base_kwargs['expires_at'] = expires_at
            elif action == ActionType.WARN:
                base_kwargs['reason'] = reason

            await log_method(**base_kwargs)

    async def handle_punitive_action(
        self,
        ctx: discord.Interaction | commands.Context,
        action: ActionType,
        target: ModerationTarget,
        reason: Optional[str] = None,
        duration_ms: Optional[int] = None,
    ):
        """Handle punitive actions (warn, mute, ban)."""
        # Validate target (localized)
        if target.user and target.server:
            await self.send_message(
                ctx,
                f'{self.bot.emotes.x_icon} '
                + t('responses.moderation.target.both', locale=self.locale),
                ephemeral=True,
            )
            return
        if not target.user and not target.server:
            await self.send_message(
                ctx,
                f'{self.bot.emotes.x_icon} '
                + t('responses.moderation.target.missing', locale=self.locale),
                ephemeral=True,
            )
            return

        if not await self._ensure_permission(ctx):
            return

        async with self.bot.db.get_session() as session:
            modsvc = ModerationService(session)

            # Map actions to infraction types
            action_mapping = {
                ActionType.WARN: InfractionType.WARNING,
                ActionType.MUTE: InfractionType.BAN,  # BAN type used for mutes
                ActionType.BAN: InfractionType.BAN,
            }

            infraction_type = action_mapping[action]

            # Check for existing infractions (except warnings)
            if action != ActionType.WARN:
                if await self._check_existing_infraction(modsvc, target, infraction_type):
                    user_field_label = t(
                        'ui.moderation.targetSelection.userField', locale=self.locale
                    )
                    server_field_label = t(
                        'ui.moderation.targetSelection.serverField', locale=self.locale
                    )
                    state = (
                        t('ui.moderation.actionNames.muted', locale=self.locale)
                        if action == ActionType.MUTE
                        else t('ui.moderation.actionNames.banned', locale=self.locale)
                    )
                    target_type = user_field_label if target.is_user else server_field_label
                    await self.send_message(
                        ctx,
                        f'{self.bot.emotes.x_icon} '
                        + t(
                            'responses.moderation.errors.alreadyState',
                            locale=self.locale,
                            targetType=target_type,
                            state=state,
                        ),
                        ephemeral=True,
                    )
                    return

            try:
                # Create infraction
                duration = duration_ms if action == ActionType.MUTE else None
                inf = await self._create_infraction(
                    modsvc, target, infraction_type, reason, duration
                )

                # Log the action
                await self._log_action(action, target, inf.reason, inf.expiresAt)

                # Send success message
                if action == ActionType.WARN:
                    action_name = t('ui.moderation.actionNames.warned', locale=self.locale)
                elif action == ActionType.MUTE:
                    action_name = t('ui.moderation.actionNames.muted', locale=self.locale)
                else:
                    action_name = t('ui.moderation.actionNames.banned', locale=self.locale)
                target_mention = (
                    target.user.mention if target.is_user and target.user else target.target_name
                )
                target_prep = (
                    t('ui.moderation.prep.in', locale=self.locale)
                    if action in [ActionType.WARN, ActionType.MUTE]
                    else t('ui.moderation.prep.from', locale=self.locale)
                )

                await self.send_message(
                    ctx,
                    f'{self.bot.emotes.tick} '
                    + t(
                        'responses.moderation.success.action',
                        locale=self.locale,
                        action=action_name,
                        target=target_mention,
                        prep=target_prep,
                        hubName=self.hub.name,
                    ),
                    ephemeral=True,
                )

            except ValueError:
                user_field_label = t('ui.moderation.targetSelection.userField', locale=self.locale)
                server_field_label = t(
                    'ui.moderation.targetSelection.serverField', locale=self.locale
                )
                state = (
                    t('ui.moderation.actionNames.muted', locale=self.locale)
                    if action == ActionType.MUTE
                    else t('ui.moderation.actionNames.banned', locale=self.locale)
                )
                target_type = user_field_label if target.is_user else server_field_label
                await self.send_message(
                    ctx,
                    f'{self.bot.emotes.x_icon} '
                    + t(
                        'responses.moderation.errors.alreadyState',
                        locale=self.locale,
                        targetType=target_type,
                        state=state,
                    ),
                    ephemeral=True,
                )

    async def send_message(
        self,
        ctx: discord.Interaction | commands.Context,
        content: str,
        ephemeral: bool = False,
    ):
        """Send a message in response to an interaction."""
        if isinstance(ctx, discord.Interaction):
            await ctx.response.send_message(content, ephemeral=ephemeral)
        else:
            await ctx.send(content, ephemeral=ephemeral)

    async def handle_revoke_action(
        self,
        ctx: discord.Interaction | commands.Context,
        action: ActionType,
        target: ModerationTarget,
    ):
        """Handle revoke actions (unmute, unban)."""
        # Validate target (localized)
        if target.user and target.server:
            await self.send_message(
                ctx,
                f'{self.bot.emotes.x_icon} '
                + t('responses.moderation.target.both', locale=self.locale),
                ephemeral=True,
            )
            return
        if not target.user and not target.server:
            await self.send_message(
                ctx,
                f'{self.bot.emotes.x_icon} '
                + t('responses.moderation.target.missing', locale=self.locale),
                ephemeral=True,
            )
            return

        if not await self._ensure_permission(ctx):
            return

        async with self.bot.db.get_session() as session:
            modsvc = ModerationService(session)

            # Get active infractions
            active = await modsvc.get_active_infractions(
                self.hub.id,
                user_id=target.target_id if target.is_user else None,
                server_id=target.target_id if target.is_server else None,
            )

            # Filter for BAN type (used for both mutes and bans)
            targets = [inf for inf in active if inf.type == InfractionType.BAN]

            if not targets:
                action_noun = (
                    t('ui.moderation.actionNouns.mute', locale=self.locale)
                    if action == ActionType.UNMUTE
                    else t('ui.moderation.actionNouns.ban', locale=self.locale)
                )
                await self.send_message(
                    ctx,
                    f'{self.bot.emotes.x_icon} '
                    + t(
                        'responses.moderation.revoke.noActive',
                        locale=self.locale,
                        action=action_noun,
                    ),
                    ephemeral=True,
                )
                return

            # Revoke all matching infractions
            for inf in targets:
                await modsvc.revoke_infraction(inf.id, str(self.moderator.id))

            # Log the action
            await self._log_action(action, target, '')

            action_noun = (
                t('ui.moderation.actionNouns.mute', locale=self.locale)
                if action == ActionType.UNMUTE
                else t('ui.moderation.actionNouns.ban', locale=self.locale)
            )
            await self.send_message(
                ctx,
                f'{self.bot.emotes.tick} '
                + t('responses.moderation.revoke.success', locale=self.locale, action=action_noun),
                ephemeral=True,
            )

    async def handle_delete_message(
        self,
        interaction: discord.Interaction,
        _message: discord.Message = None,  # type: ignore
        reason: Optional[str] = None,
    ):
        """Handle message deletion (placeholder implementation)."""
        await interaction.response.send_message(
            f'{self.bot.emotes.info_icon} '
            + t(
                'responses.moderation.delete.notImplemented',
                locale=self.locale,
                reason=reason or 'N/A',
            ),
            ephemeral=True,
        )

    async def handle_global_blacklist_action(
        self,
        ctx: discord.Interaction | commands.Context,
        target: ModerationTarget,
        reason: Optional[str] = None,
        duration_ms: Optional[int] = None,
    ):
        """Create a global blacklist entry (user or server)."""
        if not is_interchat_staff_direct(self.bot, str(self.moderator.id)):
            await self.send_message(
                ctx,
                f'{self.bot.emotes.x_icon} '
                + t('responses.moderation.blacklist.permissionDenied', locale=self.locale),
                ephemeral=True,
            )
            return
        is_valid, error_msg = target.validate()
        if not is_valid:
            await self.send_message(ctx, f'{self.bot.emotes.x_icon} {error_msg}', ephemeral=True)
            return
        async with self.bot.db.get_session() as session:
            modsvc = ModerationService(session)
            try:
                if target.is_user:
                    await modsvc.create_blacklist_entry(
                        user_id=target.target_id,
                        mod_id=str(self.moderator.id),
                        reason=reason or NO_REASON,
                        duration_ms=duration_ms,
                    )
                else:
                    await modsvc.create_server_blacklist_entry(
                        server_id=target.target_id,
                        mod_id=str(self.moderator.id),
                        reason=reason or NO_REASON,
                        duration_ms=duration_ms,
                    )
            except ValueError:
                await self.send_message(
                    ctx,
                    f'{self.bot.emotes.x_icon} '
                    + t('responses.moderation.blacklist.alreadyActive', locale=self.locale),
                    ephemeral=True,
                )
                return
        mention = target.user.mention if target.is_user and target.user else target.target_name
        await self.send_message(
            ctx,
            f'{self.bot.emotes.tick} '
            + t('responses.moderation.blacklist.success', locale=self.locale, target=mention),
            ephemeral=True,
        )


class Moderation(commands.Cog):
    """Moderation commands for hub management."""

    def __init__(self, bot: 'Bot'):
        self.bot = bot
        self.constants = InterchatConstants()
        self.locale = 'en'

    @commands.hybrid_group()
    async def mod(self, _ctx: commands.Context[commands.Bot]):
        """Moderation commands for hub management."""
        pass

    async def _fetch_hub(self, hub_id: str) -> Optional[Hub]:
        """Fetch hub by ID."""
        async with self.bot.db.get_session() as session:
            stmt = select(Hub).where(Hub.id == hub_id)
            return (await session.execute(stmt)).scalar_one_or_none()

    async def _parse_hub_from_json(self, hub: str) -> Optional[dict]:
        """Parse hub JSON string safely."""
        try:
            return json.loads(hub)
        except json.JSONDecodeError:
            return None

    async def _validate_and_get_hub(self, ctx: commands.Context, hub_json: str) -> Optional[Hub]:
        """Validate and fetch hub from JSON string."""
        hub_obj = await self._parse_hub_from_json(hub_json)
        if not hub_obj or 'id' not in hub_obj:
            await ctx.send(
                f'{self.bot.emotes.x_icon} '
                + t('responses.moderation.errors.invalidHubData', locale=self.locale),
                ephemeral=True,
            )
            return None

        selected_hub = await self._fetch_hub(hub_obj['id'])
        if not selected_hub:
            await ctx.send(
                f'{self.bot.emotes.x_icon} '
                + t('ui.common.messages.hubNotFound', locale=self.locale),
                ephemeral=True,
            )
            return None

        return selected_hub

    async def _ensure_interaction(self, ctx: commands.Context):
        """Ensure context has an interaction."""
        if not ctx.interaction:
            raise NoInteraction()

    @mod.command(
        name='panel',
        description=t('commands.mod.panel.description', locale='en'),
        extras={'category': 'Moderation'},
    )
    async def panel(
        self,
        ctx: commands.Context[commands.Bot],
        user: Optional[discord.User] = None,
        message: discord.Message = None,  # type: ignore
        server: discord.Guild = None,  # type: ignore
    ):
        """Open moderation panel."""
        await ctx.defer()

        # Auto-detect replied message if no message provided
        if not message and ctx.message.reference:
            ref = ctx.message.reference
            if ref and isinstance(ref.resolved, discord.Message):
                message = ref.resolved

        user_hubs = await get_user_moderated_hubs(self.bot, str(ctx.author.id))
        if not user_hubs:
            return await ctx.send(
                f'{self.bot.emotes.x_icon} '
                + t('responses.moderation.errors.noModeratedHubs', locale=self.locale),
                ephemeral=True,
            )

        if message:
            # Handle message-based moderation
            await self._handle_message_panel(ctx, message)
        else:
            # Handle user/server selection
            await self._handle_target_panel(ctx, user, server, user_hubs)

    async def _handle_message_panel(self, ctx: commands.Context, message: discord.Message):
        """Handle moderation panel for a specific message."""
        async with self.bot.db.get_session() as session:
            msg_result = await fetch_original_msg_with_extra(session, str(message.id))

        if not msg_result:
            return await ctx.send(
                f'{self.bot.emotes.x_icon} '
                + t('responses.moderation.errors.originalMessageNotFound', locale=self.locale),
                ephemeral=True,
            )

        original_message, _, _ = msg_result

        try:
            target_user = await self.bot.fetch_user(int(original_message.authorId))
            target_server = await self.bot.fetch_guild(int(original_message.guildId))
        except (discord.NotFound, ValueError):
            return await ctx.send(
                f'{self.bot.emotes.x_icon} '
                + t('responses.moderation.errors.fetchAuthorOrServerFailed', locale=self.locale),
                ephemeral=True,
            )

        # Get the hub from the message
        selected_hub = await self._fetch_hub(original_message.hubId)
        if not selected_hub:
            return await ctx.send(
                f'{self.bot.emotes.x_icon} '
                + t('responses.moderation.errors.hubNotFoundForMessage', locale=self.locale),
                ephemeral=True,
            )

        # Show the mod panel directly
        view = ModPanelView(
            self.bot,
            ctx.author,
            target_user,
            target_server,
            message,
            selected_hub,
            self.constants,
            self.locale,
        )

        embed = mod_panel_embed(
            self.bot,
            selected_hub,
            target_user,
            target_server,
            message,
            user_reputation=69,
            user_infractions=69,
            server_infractions=69,
            _locale=self.locale,
        )
        await ctx.send(embed=embed, view=view)

    async def _handle_target_panel(
        self,
        ctx: commands.Context,
        user: Optional[discord.User],
        server: Optional[discord.Guild],
        user_hubs: list,
    ):
        """Handle moderation panel for user/server targets."""
        if not user and not server:
            return await ctx.send(
                f'{self.bot.emotes.x_icon} You must provide either a user, server, or message parameter.',
                ephemeral=True,
            )

        # Show hub selection view
        view = HubSelectionView(
            self.bot,
            ctx.author,
            user,
            server,
            None,
            user_hubs,
            self.constants,
            self.locale,
        )

        embed = discord.Embed(
            title=t('ui.moderation.hubSelection.title', locale=self.locale),
            description=t('ui.moderation.hubSelection.description', locale=self.locale),
            color=self.constants.color(),
        )

        if user:
            embed.add_field(
                name=t('ui.moderation.targetSelection.userField', locale=self.locale),
                value=f'{user.mention} (`{user.id}`)',
                inline=True,
            )
        if server:
            embed.add_field(
                name=t('ui.moderation.targetSelection.serverField', locale=self.locale),
                value=f'**{server.name}** (`{server.id}`)',
                inline=True,
            )

        embed.add_field(
            name=t('ui.moderation.hubSelection.fieldHubLabel', locale=self.locale),
            value=t('ui.moderation.hubSelection.fieldHubPrompt', locale=self.locale),
            inline=False,
        )

        await ctx.send(embed=embed, view=view)

    async def _execute_mod_action(
        self,
        ctx: commands.Context['Bot'],
        action: ActionType,
        hub: str,
        user: Optional[discord.User] = None,
        server: Optional[discord.Guild] = None,
        reason: Optional[str] = None,
    ):
        """Execute a moderation action with common validation."""
        await self._ensure_interaction(ctx)

        selected_hub = await self._validate_and_get_hub(ctx, hub)
        if not selected_hub:
            return

        target = ModerationTarget(user=user, server=server)
        handler = ModerationActionHandler(
            self.bot, ctx.author, selected_hub, self.constants, self.locale
        )

        if action in [ActionType.WARN, ActionType.MUTE, ActionType.BAN]:
            await handler.handle_punitive_action(ctx, action, target, reason)
        elif action in [ActionType.UNMUTE, ActionType.UNBAN]:
            await handler.handle_revoke_action(ctx, action, target)

    @mod.command(
        name='ban',
        description=t('commands.mod.ban.description', locale='en'),
        extras={'category': 'Moderation'},
    )
    @hubm_autocomplete
    async def ban(
        self,
        ctx: commands.Context['Bot'],
        hub: str,
        user: Optional[discord.User] = None,
        server: discord.Guild = None,  # type: ignore
        reason: Optional[str] = None,
    ):
        await self._execute_mod_action(ctx, ActionType.BAN, hub, user, server, reason)

    @mod.command(
        name='mute',
        description=t('commands.mod.mute.description', locale='en'),
        extras={'category': 'Moderation'},
    )
    @hubm_autocomplete
    async def mute(
        self,
        ctx: commands.Context['Bot'],
        hub: str,
        user: Optional[discord.User] = None,
        server: discord.Guild = None,  # type: ignore
        reason: Optional[str] = None,
    ):
        await self._execute_mod_action(ctx, ActionType.MUTE, hub, user, server, reason)

    @mod.command(
        name='warn',
        description=t('commands.mod.warn.description', locale='en'),
        extras={'category': 'Moderation'},
    )
    @hubm_autocomplete
    async def warn(
        self,
        ctx: commands.Context['Bot'],
        hub: str,
        user: Optional[discord.User] = None,
        server: discord.Guild = None,  # type: ignore
        reason: Optional[str] = None,
    ):
        await self._execute_mod_action(ctx, ActionType.WARN, hub, user, server, reason)

    @mod.command(
        name='unmute',
        description=t('commands.mod.unmute.description', locale='en'),
        extras={'category': 'Moderation'},
    )
    @hubm_autocomplete
    async def unmute(
        self,
        ctx: commands.Context['Bot'],
        hub: str,
        user: Optional[discord.User] = None,
        server: discord.Guild = None,  # type: ignore
    ):
        await self._execute_mod_action(ctx, ActionType.UNMUTE, hub, user, server)

    @mod.command(
        name='unban',
        description=t('commands.mod.unban.description', locale='en'),
        extras={'category': 'Moderation'},
    )
    @hubm_autocomplete
    async def unban(
        self,
        ctx: commands.Context['Bot'],
        hub: str,
        user: Optional[discord.User] = None,
        server: discord.Guild = None,  # type: ignore
    ):
        await self._execute_mod_action(ctx, ActionType.UNBAN, hub, user, server)

    @mod.command(
        name='delete',
        description=t('commands.mod.delete.description', locale='en'),
        extras={'category': 'Moderation'},
    )
    @hubm_autocomplete
    async def delete_infraction(
        self,
        ctx: commands.Context['Bot'],
        hub: str,
        infraction_id: str,
    ):
        """Delete an infraction (requires Manager+ permissions)."""
        await ctx.defer()

        selected_hub = await self._validate_and_get_hub(ctx, hub)
        if not selected_hub:
            return

        # Check Manager+ permission
        perm = get_user_permission(selected_hub, str(ctx.author.id))
        if perm < HubPermissionLevel.MANAGER:
            return await ctx.send(
                f'{self.bot.emotes.x_icon} '
                + t('responses.moderation.permissions.managerRequired', locale=self.locale),
                ephemeral=True,
            )

        # Perform delete
        async with self.bot.db.get_session() as session:
            modsvc = ModerationService(session)
            success = await modsvc.delete_infraction(infraction_id)

        if not success:
            return await ctx.send(
                f'{self.bot.emotes.x_icon} '
                + t('responses.moderation.delete.notFound', locale=self.locale),
                ephemeral=True,
            )

        await ctx.send(
            f'{self.bot.emotes.tick} '
            + t('responses.moderation.delete.success', locale=self.locale),
            ephemeral=True,
        )


async def setup(bot: 'Bot'):
    await bot.add_cog(Moderation(bot))
