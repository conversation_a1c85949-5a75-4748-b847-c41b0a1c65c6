from typing import TYPE_CHECKING, Sequence, <PERSON><PERSON>

import discord
from discord import ui
from discord.ext import commands
from sqlalchemy import desc, select

from utils.modules.ui.views.connectionManagement import ConnectionView
from utils.modules.core.checks import interaction_check
from utils.modules.core.db.models import Connection, Hub
from utils.modules.core.i18n import t
from utils.utils import check_user, load_user_locale, fetch_connections
from utils.modules.core.webhookCore import fix_connections

if TYPE_CHECKING:
    from main import Bot


class Connections(commands.Cog):
    def __init__(self, bot):
        self.bot: 'Bot' = bot

    @commands.hybrid_group()
    async def manage(self, ctx: commands.Context[commands.Bot]):
        pass

    @manage.command(
        name='connections',
        description='Manage your connections.',
        extras={'category': 'Connections'}
    )
    @commands.has_permissions(manage_channels=True)
    @check_user()
    async def manage_connections(self, ctx: commands.Context[commands.Bot]):
        """Manage your connections."""
        await ctx.defer(ephemeral=False)
        locale = await load_user_locale(self.bot, ctx)
        result = await fetch_connections(self.bot, str(ctx.guild.id))

        if result:
            embed = discord.Embed(
                title=t('commands.connections.title', locale),
                description=t('commands.connections.description', locale),
                color=self.bot.constants.color(),
            )
            for con, hub_name in result:
                embed.add_field(
                    name=f'<#{con.channelId}>',
                    value=(
                        f'> **{t("responses.common.hub", locale)}:** {hub_name}\n'
                        f'> **{t("commands.connections.fields.lastActive", locale)}:** <t:{int(con.lastActive.timestamp())}:R>'
                    ),
                    inline=True,
                )

            view = ConnectionView(self.bot, ctx.author, result, locale)
            await ctx.send(embed=embed, view=view)

        else:
            embed = discord.Embed(
                title=t('responses.errors.whoops', locale),
                description=t(
                    'responses.errors.notConnectedServer',
                    locale,
                    cross=self.bot.emotes.x_icon
                ),
                color=discord.Color.red()
            )
            await ctx.send(embed=embed)

    @commands.hybrid_group()
    @check_user()
    async def fix(self, ctx: commands.Context):
        pass

    @fix.command(name='connections')
    @commands.has_permissions(manage_channels=True)
    @check_user()
    async def fix_connections(self, ctx: commands.Context[commands.Bot]):
        await ctx.defer()
        locale = await load_user_locale(self.bot, ctx)
        fixed_connections, user_error = await fix_connections(self.bot, ctx.guild, locale)
        embed = discord.Embed(title=f'{t("commands.connections.fix.title", locale)}', description=f'{t("commands.connections.fix.description", locale)}', color=discord.Color.green() if fixed_connections and not user_error else discord.Color.orange())
        for conn, status in fixed_connections:
            embed.add_field(name=f'<#{conn.channelId}>', value=status, inline=True)
        for conn, status in user_error:
            embed.add_field(name=f'<#{conn.channelId}>', value=status, inline=True)

        await ctx.send(embed=embed, ephemeral=True)

async def setup(bot: 'Bot'):
    await bot.add_cog(Connections(bot))
