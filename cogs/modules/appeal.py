import locale
from typing import TYPE_CHECKING, List, Optional, TypedDict

import discord
from discord.ext import commands
from sqlalchemy import and_, desc, or_, select
from sqlalchemy.orm import selectinload

from utils.constants import logger
from utils.modules.core.i18n import t, tu
from utils.utils import load_user_locale
from datetime import datetime, timedelta
from utils.modules.core.db.models import (
    Infraction,
    InfractionStatus,
    InfractionType,
    Appeal,
    AppealStatus,
    User,
)
from utils.modules.events.eventDispatcher import create_hub_event, HubEventType, event_dispatcher


if TYPE_CHECKING:
    from main import Bot

# Character limits (Discord modal text inputs).
LIMIT_Q1 = 300
LIMIT_Q2 = 300
LIMIT_Q3 = 300


class PaginatorView(discord.ui.View):
    """Simple paginator view for appeals list."""

    def __init__(self, *, bot: 'Bot', build_embed, per_page: int, total: int, owner_id: int):
        super().__init__(timeout=180)
        self.bot = bot
        self.build_embed = build_embed
        self.per_page = per_page
        self.total = total
        self.page = 0
        self.owner_id = owner_id

        self.prev_button = discord.ui.Button(
            label=t('ui.appeal.buttons.previous', locale='en'),
            style=discord.ButtonStyle.secondary,
            emoji='⬅️',
        )
        self.next_button = discord.ui.Button(
            label=t('ui.appeal.buttons.next', locale='en'),
            style=discord.ButtonStyle.secondary,
            emoji='➡️',
        )
        self.page_button = discord.ui.Button(
            label=self._page_label(), style=discord.ButtonStyle.gray, disabled=True
        )
        self.prev_button.callback = self.on_prev
        self.next_button.callback = self.on_next

        self.add_item(self.prev_button)
        self.add_item(self.page_button)
        self.add_item(self.next_button)
        self._update_buttons()

    def _page_label(self) -> str:
        last_page = max(0, (self.total - 1) // self.per_page)
        return f'Page {self.page + 1}/{last_page + 1 if self.total else 1}'

    def _update_buttons(self):
        last_page = max(0, (self.total - 1) // self.per_page)
        self.prev_button.disabled = self.page <= 0
        self.next_button.disabled = self.page >= last_page
        self.page_button.label = self._page_label()

    async def _edit(self, interaction: discord.Interaction):
        if interaction.user.id != self.owner_id:
            await interaction.response.send_message(
                t('ui.appeal.errors.cannotControl', locale='en', x_icon=self.bot.emotes.x_icon),
                ephemeral=True,
            )
            return

        self._update_buttons()
        embed = await self.build_embed(self.page)
        await interaction.response.edit_message(embed=embed, view=self)

    async def on_prev(self, interaction: discord.Interaction):
        if self.page > 0:
            self.page -= 1
        await self._edit(interaction)

    async def on_next(self, interaction: discord.Interaction):
        last_page = max(0, (self.total - 1) // self.per_page)
        if self.page < last_page:
            self.page += 1
        await self._edit(interaction)


class AppealSelectView(discord.ui.View):
    """View for selecting an infraction to appeal."""

    def __init__(self, bot: 'Bot', infractions: List[Infraction], user_id: str, locale):
        super().__init__(timeout=300)
        self.bot = bot
        self.infractions = infractions
        self.user_id = user_id  # stored as string for consistency with DB
        self.locale = locale

        if not infractions:
            return

        options: list[discord.SelectOption] = []
        for inf in infractions[:25]:  # Discord hard limit
            hub_name = (
                inf.hub.name if inf.hub else t('responses.appeal.constants.unknownHub', locale=self.locale)
            )
            infraction_type = inf.type.name.title()
            created_date = inf.createdAt.strftime('%Y-%m-%d')

            description = f'{hub_name} • {created_date}'
            if inf.reason and len(inf.reason) > 50:
                description += f' • {inf.reason[:47]}...'
            elif inf.reason:
                description += f' • {inf.reason}'

            options.append(
                discord.SelectOption(
                    label=f'{infraction_type} in {hub_name}',
                    description=description,
                    value=inf.id,
                    emoji=self.bot.emotes.hammer_icon
                    if inf.type == InfractionType.BAN
                    else self.bot.emotes.alert_icon,
                )
            )

        if options:
            select = discord.ui.Select(
                placeholder=t('ui.appeal.select.placeholder', locale='en'),
                options=options,
                custom_id='appeal_select',  # allows persistence later if needed
                min_values=1,
                max_values=1,
            )

            async def _callback(interaction: discord.Interaction['Bot']):
                logger.debug(
                    'AppealSelectView interaction received: user=%s component_id=%s values=%s',
                    interaction.user.id,
                    interaction.data.get('custom_id') if interaction.data else 'N/A',
                    getattr(select, 'values', []),
                )

                if interaction.user.id != int(self.user_id):
                    await interaction.response.send_message(
                        t(
                            'ui.appeal.errors.notYourMenu',
                            locale=self.locale,
                            x_icon=self.bot.emotes.x_icon,
                        ),
                        ephemeral=True,
                    )
                    return

                if not select.values:
                    await interaction.response.send_message(
                        t(
                            'ui.appeal.errors.nothingSelected',
                            locale=self.locale,
                            x_icon=self.bot.emotes.x_icon,
                        ),
                        ephemeral=True,
                    )
                    return

                selected_id = select.values[0]
                selected_infraction = next(
                    (inf for inf in self.infractions if inf.id == selected_id), None
                )
                if not selected_infraction:
                    await interaction.response.send_message(
                        t(
                            'ui.appeal.errors.invalidSelection',
                            locale=self.locale,
                            x_icon=self.bot.emotes.x_icon,
                        ),
                        ephemeral=True,
                    )
                    return

                try:
                    modal = AppealModal(self.bot, selected_infraction, self.locale)
                    await interaction.response.send_modal(modal)
                except Exception as e:
                    logger.error('Failed to send appeal modal: %s', e)
                    if not interaction.response.is_done():
                        await interaction.response.send_message(
                            t(
                                'ui.appeal.errors.modalError',
                                locale=self.locale,
                                x_icon=self.bot.emotes.x_icon,
                            ),
                            ephemeral=True,
                        )

            select.callback = _callback  # attach runtime callback
            self.add_item(select)


class AppealModal(discord.ui.Modal):
    def __init__(self, bot: 'Bot', infraction: Infraction, locale):
        super().__init__(title=t('ui.appeal.modal.title', locale='en'))
        self.bot = bot
        self.infraction = infraction
        self.locale = locale

        self.q1 = discord.ui.TextInput(
            label=t('ui.appeal.modal.q1', locale='en'),
            style=discord.TextStyle.paragraph,
            max_length=LIMIT_Q1,
            required=True,
        )
        self.q2 = discord.ui.TextInput(
            label=t('ui.appeal.modal.q2', locale='en'),
            style=discord.TextStyle.paragraph,
            max_length=LIMIT_Q2,
            required=True,
        )
        self.q3 = discord.ui.TextInput(
            label=t('ui.appeal.modal.q3', locale='en'),
            style=discord.TextStyle.paragraph,
            max_length=LIMIT_Q3,
            required=False,
        )
        self.add_item(self.q1)
        self.add_item(self.q2)
        self.add_item(self.q3)

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True)
        reason_combined = await tu(
            self.bot,
            interaction,
            'appeal.combined.reason',
            q1=self.q1.value,
            q2=self.q2.value,
            q3=self.q3.value
            or await tu(self.bot, interaction, 'appeal.form.questions.notApplicable'),
        )

        async with self.bot.db.get_session() as session:
            # Refresh infraction to ensure still active and appealable
            stmt = (
                select(Infraction)
                .where(Infraction.id == self.infraction.id)
                .options(selectinload(Infraction.hub))
            )
            res = await session.execute(stmt)
            inf = res.scalar_one_or_none()
            if not inf or (inf.expiresAt is not None and inf.expiresAt < datetime.now()):
                await interaction.response.send_message(
                    t('commands.appeal.notAppealable', locale='en', x_icon=self.bot.emotes.x_icon),
                    ephemeral=True,
                )
                return

            # Fetch or create user model
            user_stmt = select(User).where(User.id == str(interaction.user.id))
            user_res = await session.execute(user_stmt)
            user_model = user_res.scalar_one_or_none()
            if not user_model:
                await interaction.response.send_message(
                    f'{self.bot.emotes.x_icon} Internal error: user record missing.', ephemeral=True
                )
                return

            # Create appeal record
            appeal = Appeal(
                infractionId=inf.id,
                userId=str(interaction.user.id),
                reason=reason_combined,
                infraction=inf,
                user=user_model,
            )  # type: ignore[call-arg]
            session.add(appeal)

            # Update infraction status
            inf.status = InfractionStatus.APPEALED
            await session.commit()

            # Dispatch appeal-submitted event for logging
            hub_name = inf.hub.name if inf.hub else 'Unknown Hub'
            event = create_hub_event(
                event_type=HubEventType.APPEAL_SUBMITTED,
                hub_id=str(inf.hubId),
                hub_name=str(hub_name),
                moderator_id=str(interaction.user.id),  # appellant
                moderator_name=str(interaction.user),
                reason=reason_combined,
                appeal_id=appeal.id,
            )
            await event_dispatcher.dispatch_hub_event(event)

        embed = discord.Embed(title=t('ui.common.titles.success', self.locale), description=t('ui.appeal.success.submitted', locale=self.locale, tick=self.bot.emotes.tick), color=discord.Color.green())
        await interaction.edit_original_response(embed=embed, view=None)


class AppealData(TypedDict):
    infraction: Infraction
    has_pending_appeal: bool
    has_any_appeal: bool
    cooldown_end: Optional[datetime]
    can_appeal_again: bool


class AppealCog(commands.Cog):
    def __init__(self, bot: 'Bot'):
        self.bot = bot
        self.constants = bot.constants
        self.locale = locale

    async def get_user_infractions_with_appeals(self, user_id: str) -> List[AppealData]:
        """Get user's appealable infractions with appeal status and cooldown info."""
        async with self.bot.db.get_session() as session:
            # Get all active ban/mute infractions for user
            stmt = (
                select(Infraction)
                .where(
                    and_(
                        Infraction.userId == user_id,
                        or_(
                            Infraction.status == InfractionStatus.ACTIVE,
                            Infraction.status == InfractionStatus.PENDING,
                        ),
                        or_(
                            Infraction.type == InfractionType.BAN,
                            Infraction.type == InfractionType.BLACKLIST,
                            Infraction.type == InfractionType.MUTE,
                        ),
                        or_(Infraction.expiresAt.is_(None), Infraction.expiresAt > datetime.now()),
                    )
                )
                .options(selectinload(Infraction.hub), selectinload(Infraction.appeals))
                .order_by(Infraction.createdAt.desc())
            )
            result = await session.execute(stmt)
            infractions = list(result.scalars().all())

            infraction_data = []
            for inf in infractions:
                # Check if already appealed
                has_pending_appeal = any(
                    appeal.status == AppealStatus.PENDING for appeal in inf.appeals
                )
                has_any_appeal = len(inf.appeals) > 0

                # Calculate cooldown
                cooldown_end = None
                can_appeal_again = True
                if has_any_appeal:
                    # Find most recent appeal
                    latest_appeal = max(inf.appeals, key=lambda a: a.createdAt)
                    hub_cooldown_hours = inf.hub.appealCooldownHours
                    cooldown_end = latest_appeal.createdAt + timedelta(hours=hub_cooldown_hours)
                    can_appeal_again = datetime.now() >= cooldown_end

                infraction_data.append(
                    {
                        'infraction': inf,
                        'has_pending_appeal': has_pending_appeal,
                        'has_any_appeal': has_any_appeal,
                        'cooldown_end': cooldown_end,
                        'can_appeal_again': can_appeal_again,
                    }
                )

            return infraction_data

    async def build_appeals_embed(
        self, page: int, user_id: str, infraction_data: List[AppealData], user_locale: str = 'en'
    ) -> discord.Embed:
        """Build embed showing user's appealable infractions."""
        per_page = 5
        start = page * per_page
        end = start + per_page
        page_data = infraction_data[start:end]

        embed = discord.Embed(
            color=self.bot.constants.color(),
            title=f'{t("commands.appeal.title", locale=user_locale)}\n',
            description=(
                f'{t("commands.appeal.description", locale=user_locale)}'
            ),
        )

        if not page_data:
            embed.add_field(
                name=f'{self.bot.emotes.tick} {t("commands.appeal.noInfractions.title", locale=user_locale)}',
                value=t('commands.appeal.noInfractions.description', locale=user_locale),
                inline=False,
            )
            return embed

        for data in page_data:
            inf = data['infraction']
            hub_name = (
                inf.hub.name
                if inf.hub
                else t('responses.appeal.constants.unknownHub', locale=user_locale)
            )
            infraction_type = inf.type.name.title()

            # Status indicators
            status_text = ''
            if data['has_pending_appeal']:
                status_text = f'> -# {self.bot.emotes.clock_icon} **{t("responses.appeal.status.pending", locale=user_locale)}**'
            elif data['has_any_appeal'] and not data['can_appeal_again']:
                cooldown_timestamp = int(data['cooldown_end'].timestamp())
                status_text = f'> -# {self.bot.emotes.cross} **{t("responses.appeal.status.cooldown", locale=user_locale)}** until <t:{cooldown_timestamp}:R>'
            elif data['has_any_appeal'] and data['can_appeal_again']:
                status_text = f'> -# {self.bot.emotes.tick} **{t("responses.appeal.status.canAppealAgain", locale=user_locale)}**'
            else:
                status_text = f'> -# {self.bot.emotes.tick} **{t("responses.appeal.status.canAppeal", locale=user_locale)}**'

            # Reason and date
            if inf.reason and len(inf.reason) <= 100:
                reason = inf.reason
            elif inf.reason:
                reason = f'{inf.reason[:97]}...'
            else:
                reason = t('responses.appeal.constants.noReason', locale=user_locale)
            created_date = f'<t:{int(inf.createdAt.timestamp())}:d>'

            field_value = f'\n> {self.bot.emotes.clock_icon} **{t("responses.appeal.fields.date", locale=user_locale)}** {created_date}\n> {self.bot.emotes.wiki_icon} **{t("responses.appeal.fields.reason", locale=user_locale)}** {reason}\n{status_text}'

            embed.add_field(
                name=f'{self.bot.emotes.hammer_icon if inf.type == InfractionType.BAN else self.bot.emotes.alert_icon} {hub_name} — {infraction_type}',
                value=field_value,
                inline=False,
            )

        # Footer with instructions
        appealable_count = sum(
            1
            for data in infraction_data
            if not data['has_pending_appeal'] and data['can_appeal_again']
        )
        if appealable_count > 0:
            embed.set_footer(
                text=t(
                    'responses.appeal.embed.footer.canAppeal',
                    locale=user_locale,
                    count=appealable_count,
                )
            )
        else:
            embed.set_footer(text=t('responses.appeal.embed.footer.checkLater', locale=user_locale))

        return embed

    @commands.hybrid_command(name='appeal', description='View and appeal your active infractions.')
    async def appeal(self, ctx: commands.Context['Bot']):
        await ctx.defer(ephemeral=True)
        logger.debug('Appeal command invoked by user_id=%s', ctx.author.id)

        # Get user's locale
        user_locale = await load_user_locale(self.bot, ctx)
        self.locale = user_locale
        # Get user's infraction data
        infraction_data = await self.get_user_infractions_with_appeals(str(ctx.author.id))

        if not infraction_data:
            embed = discord.Embed(
                title=t('responses.appeal.embed.noInfractions.title', locale=user_locale),
                description=f'{self.bot.emotes.tick} {t("responses.appeal.embed.noInfractions.description", locale=user_locale)}',
                color=discord.Color.green(),
            )
            return await ctx.send(embed=embed, ephemeral=True)
        

        # Get appealable infractions (for select menu)
        appealable_infractions = [
            data['infraction']
            for data in infraction_data
            if not data['has_pending_appeal'] and data['can_appeal_again']
        ]

        # Build initial embed
        embed = await self.build_appeals_embed(0, str(ctx.author.id), infraction_data, user_locale)

        # Build composite view: select menu (if any appealable infractions) + optional paginator
        if appealable_infractions:
            view: discord.ui.View = AppealSelectView(
                bot=self.bot,
                infractions=appealable_infractions,
                user_id=str(ctx.author.id),
                locale=self.locale
            )
        else:
            view = discord.ui.View(timeout=300)

        # Add pagination items to existing view if needed
        if len(infraction_data) > 5:
            paginator = PaginatorView(
                bot=self.bot,
                build_embed=lambda page: self.build_appeals_embed(
                    page, str(ctx.author.id), infraction_data
                ),
                per_page=5,
                total=len(infraction_data),
                owner_id=ctx.author.id,
            )
            for item in paginator.children:
                view.add_item(item)

        logger.debug(
            'Sending appeals embed: user_id=%s appealable=%d total=%d paginator=%s',
            ctx.author.id,
            len(appealable_infractions),
            len(infraction_data),
            'yes' if len(infraction_data) > 5 else 'no',
        )
        await ctx.send(embed=embed, view=view, ephemeral=True)


async def setup(bot: 'Bot'):
    await bot.add_cog(AppealCog(bot))
