from datetime import datetime
import json

import discord
from discord.ext import commands

from sqlalchemy import select
from sqlalchemy.exc import IntegrityError

from utils.modules.core.db.models import Connection, Hub, HubInvite
from utils.modules.ui.views.hubAnnounceViews import OpenView
from utils.modules.ui.views.hubConfig.hubConfigurationView import ConfigurationView
from utils.modules.ui.views.hubConfig.inviteManagementView import InviteView
from utils.modules.core.webhookCore import (
    fetch_or_create_webhook,
    get_and_cleanup_webhooks,
)
from utils.modules.ui.views.hubCreateView import HubCreationView
from utils.modules.hub.constants import HubPermissionLevel
from utils.modules.hub.utils import get_user_permission
from utils.modules.ui.AutoComplete import (
    hubm_autocomplete,
    hubo_autocomplete,
    hubp_autocomplete,
)
from utils.modules.core.i18n import t
from utils.modules.errors.customDiscord import (
    InvalidInput,
    InvalidInvite,
    WebhookError,
    NotConnected,
)
from utils.utils import check_user

from typing import TYPE_CHECKING, Optional

if TYPE_CHECKING:
    from main import Bot


class Hubs(commands.Cog):
    def __init__(self, bot: 'Bot'):
        self.bot: Bot = bot
        self.constants = bot.constants

    @commands.hybrid_command(
        name='hubs', description='View InterChat hubs', extras={'category': 'Hubs'}
    )
    @check_user()
    async def hubs(self, ctx: commands.Context[commands.Bot]):
        async with self.bot.db.get_session() as session:
            hubs = (await session.execute(select(Hub))).scalars().all()
            if not hubs:
                await ctx.send(content=t('responses.hub.noHubsFound', locale='en'))
                return

            content = ''
            for hub in hubs:
                content += f'{hub.name} ({hub.id}): {hub.shortDescription} | {hub.description}\n'
            await ctx.send(content=content)

    @commands.hybrid_group()
    async def hub(self, ctx: commands.Context[commands.Bot]):
        pass

    @hub.command(
        name='create',
        description='Create an InterChat hub',
        extras={'category': 'Hubs'},
    )
    @check_user()
    async def create_hub(self, ctx: commands.Context[commands.Bot]):
        """Interactive hub creation command"""
        embed = discord.Embed(
            description=(
                f'### {self.bot.emotes.topggSparkles} {t("ui.hub.creation.title", locale="en")}\n'
                f'{t("ui.hub.creation.description", locale="en")}\n\n'
                f'{t("ui.hub.creation.clickToBegin", locale="en", emoji=self.bot.emotes.wand_icon)}'
            ),
            color=self.constants.color(),
        )
        embed.set_author(name=f'@{ctx.author.name}', icon_url=ctx.author.display_avatar.url)
        view = HubCreationView(self.bot, ctx.author)
        msg = await ctx.send(embed=embed, view=view, ephemeral=True)
        view.message = msg

    @hub.command(
        name='configure',
        description='Configure your InterChat hub',
        extras={'category': 'Hubs'},
    )
    @hubm_autocomplete
    @check_user()
    async def configure(self, ctx: commands.Context[commands.Bot], hub):
        hub_obj = json.loads(hub)

        async with self.bot.db.get_session() as session:
            db_hub = await session.scalar(select(Hub).where(Hub.id == hub_obj['id']))

            if not db_hub:
                raise InvalidInput()

            user_level = get_user_permission(db_hub, str(ctx.author.id))
            options = []

            if user_level >= HubPermissionLevel.MANAGER:
                options += [
                    discord.SelectOption(
                        emoji=self.bot.emotes.house_icon,
                        label=t('ui.hub.config.options.general.label', locale='en'),
                        description=t('ui.hub.config.options.general.description', locale='en'),
                        value='hgeneral',
                    ),
                    discord.SelectOption(
                        emoji=self.bot.emotes.rules_icon,
                        label=t('ui.hub.config.options.rules.label', locale='en'),
                        description=t('ui.hub.config.options.rules.description', locale='en'),
                        value='hrules',
                    ),
                    discord.SelectOption(
                        emoji=self.bot.emotes.person_icon,
                        label=t('ui.hub.config.options.team.label', locale='en'),
                        description=t('ui.hub.config.options.team.description', locale='en'),
                        value='hperm',
                    ),
                    discord.SelectOption(
                        emoji=self.bot.emotes.gear_icon,
                        label=t('ui.hub.config.options.modules.label', locale='en'),
                        description=t('ui.hub.config.options.modules.description', locale='en'),
                        value='hmodules',
                    ),
                    discord.SelectOption(
                        emoji=self.bot.emotes.hash_icon,
                        label=t('ui.hub.config.options.logging.label', locale='en'),
                        description=t('ui.hub.config.options.logging.description', locale='en'),
                        value='hlogging',
                    ),
                ]

            if user_level >= HubPermissionLevel.OWNER:
                options += [
                    discord.SelectOption(
                        emoji=self.bot.emotes.globe_icon,
                        label=t('ui.hub.config.options.transfer.label', locale='en'),
                        description=t('ui.hub.config.options.transfer.description', locale='en'),
                        value='transfer',
                    )
                ]

            if user_level >= HubPermissionLevel.MODERATOR:
                options.append(
                    discord.SelectOption(
                        emoji=self.bot.emotes.chat_icon,
                        label=t('ui.hub.config.options.announcements.label', locale='en'),
                        description=t(
                            'ui.hub.config.options.announcements.description', locale='en'
                        ),
                        value='hsannounce',
                    )
                )

            embed = discord.Embed(
                description=f'### {self.bot.emotes.gear_icon} {t("ui.hub.config.title", locale="en")}\n{t("ui.hub.config.description", locale="en")}',
                color=self.constants.color(),
            )
            view = ConfigurationView(self.bot, ctx.author, db_hub, options, user_level)
            message = await ctx.send(embed=embed, view=view)
            view.message = message

    @hubo_autocomplete
    @check_user()
    async def delete_hub(self, ctx: commands.Context[commands.Bot], hub: str):
        hub_obj = json.loads(hub)
        async with self.bot.db.get_session() as session:
            stmt = select(Hub).where(Hub.id == hub_obj['id'])
            result = await session.scalar(stmt)

            # confirm here with result found above
            await session.delete(result)
            await session.commit()

        embed = discord.Embed(
            title='Success!',
            description=f'{self.bot.emotes.tick} Your hub has been deleted successfully.',
            color=discord.Color.green(),
        )
        await ctx.send(embed=embed, ephemeral=True)

    @commands.hybrid_command(
        name='connect', description='Join an InterChat hub', extras={'category': 'Hubs'}
    )
    @commands.has_permissions(manage_channels=True)
    @hubp_autocomplete
    @check_user()
    @commands.guild_only()
    async def connect(
        self,
        ctx: commands.Context[commands.Bot],
        hub: Optional[str] = None,
        invite: Optional[str] = None,
        channel: Optional[discord.TextChannel | discord.Thread] = None,
    ):
        await ctx.defer(ephemeral=False)
        if hub is None and invite is None:
            raise InvalidInput()

        if not ctx.guild:
            raise commands.NoPrivateMessage()

        selected_channel = channel or ctx.channel
        if not isinstance(selected_channel, discord.abc.GuildChannel):
            raise commands.NoPrivateMessage()

        result_invite = None
        result_hub = None

        if invite:
            async with self.bot.db.get_session() as session:
                stmt = select(HubInvite).where(HubInvite.code == invite)
                result_invite = await session.scalar(stmt)

                if not result_invite:
                    raise InvalidInvite()

                if result_invite.expires and result_invite.expires <= datetime.now():
                    await session.delete(result_invite)
                    await session.commit()
                    raise InvalidInvite()

                result_invite.uses += 1

                stmt = select(Hub).where(Hub.id == result_invite.hubId)
                result_hub = await session.scalar(stmt)

                if result_invite.maxUses == result_invite.uses:
                    await session.delete(result_invite)

                await session.commit()

        elif hub:
            hub_data = json.loads(hub)
            hub_id = hub_data.get('id')

            async with self.bot.db.get_session() as session:
                stmt = select(Hub).where((Hub.id == hub_id) & (Hub.private.is_(False)))
                result_hub = await session.scalar(stmt)

                if not result_hub:
                    raise InvalidInput()

        if result_hub is None:
            raise InvalidInput()

        webhook = await fetch_or_create_webhook(self.bot, selected_channel)

        if not webhook:
            raise WebhookError()

        connection = Connection(
            hubId=result_hub.id,
            channelId=str(selected_channel.id),
            webhookURL=webhook.url,
            parentId=(
                str(selected_channel.parent_id)
                if isinstance(selected_channel, discord.Thread)
                else None
            ),
            invite=result_invite.code if result_invite else None,
            serverId=str(ctx.guild.id),
            createdAt=datetime.now(),
            lastActive=datetime.now(),
        )

        try:
            async with self.bot.db.get_session() as session:
                session.add(connection)
                await session.commit()
        except IntegrityError as e:
            if 'duplicate key value violates unique constraint' in str(
                e
            ) and 'Connection_hubId_serverId_key' in str(e):
                embed = discord.Embed(
                    title='Already connected!',
                    description=f'{self.bot.emotes.x_icon} This server is already connected to **{result_hub.name}** from **{ctx.guild.name}**!',
                    color=discord.Color.red(),
                )
                return await ctx.send(embed=embed, ephemeral=True)

        embed = discord.Embed(
            title='Connected!',
            description=f'{self.bot.emotes.tick} Connected to **{result_hub.name}** - get chatting!',
            color=self.constants.color(),
        )
        embed.set_author(name=f'@{ctx.author.name}', icon_url=ctx.author.display_avatar.url)
        embed.set_footer(text=f'Connected Channel: {selected_channel}')
        await ctx.send(embed=embed)

    @commands.hybrid_command(
        name='disconnect',
        description='Leave an InterChat hub',
        extras={'category': 'Hubs'},
    )
    @commands.has_permissions(manage_channels=True)
    @check_user()
    @commands.guild_only()
    async def leave(self, ctx: commands.Context[commands.Bot]):
        if not isinstance(ctx.channel, discord.abc.GuildChannel):
            raise commands.NoPrivateMessage()

        async with self.bot.db.get_session() as session:
            stmt = select(Connection).where(Connection.channelId == str(ctx.channel.id))
            result = await session.scalar(stmt)

            if not result:
                raise NotConnected()

            await session.delete(result)
            await session.commit()

        await get_and_cleanup_webhooks(self.bot, ctx.channel)

        embed = discord.Embed(
            title='Disconnected!',
            description=f'{self.bot.emotes.tick} Disconnected from hub!',
            color=self.constants.color(),
        )
        embed.set_author(name=f'@{ctx.author.name}', icon_url=ctx.author.display_avatar.url)
        embed.set_footer(text=f'Disconnected Channel: {ctx.channel}')
        await ctx.send(embed=embed)

    @hub.command(
        name='invites',
        description='View all active invites for a hub',
        extras={'category': 'Hubs'},
    )
    @hubm_autocomplete
    @check_user()
    async def invites(self, ctx: commands.Context[commands.Bot], hub: str):
        hub_data = json.loads(hub)
        hub_id = hub_data['id']
        hub_name = hub_data['name']

        async with self.bot.db.get_session() as session:
            stmt = select(HubInvite).where(HubInvite.hubId == hub_id)
            result = await session.execute(stmt)
            all_invites = result.scalars().all()

            expired_invites = []
            active_invites = []

            current_time = datetime.now()

            for invite in all_invites:
                if invite.expires and invite.expires <= current_time:
                    expired_invites.append(invite)
                else:
                    active_invites.append(invite)

            if expired_invites:
                for expired_invite in expired_invites:
                    await session.delete(expired_invite)
                await session.commit()

        embed = discord.Embed(title='Active invites', description=' ', color=self.constants.color())
        embed.set_footer(text=f'Hub: {hub_name}')

        if active_invites:
            for invite in active_invites:
                if invite.maxUses == 0:
                    muses = '∞'
                else:
                    muses = invite.maxUses
                embed.add_field(
                    name=f'{self.bot.emotes.link_icon} ||{invite.code}||',
                    value=f'\n> **Uses:** {invite.uses}/{muses}\n> **Expires:** <t:{int(invite.expires.timestamp())}:R>',
                    inline=True,
                )
        else:
            embed.description = f'{self.bot.emotes.x_icon} None found'

        view = InviteView(self.bot, self.constants, ctx.author, hub_data)
        view.setup_button()
        await ctx.send(embed=embed, view=view, ephemeral=True)

    @hub.command(name='announce', description='none', extras={'category': 'Hubs'})
    @hubm_autocomplete
    @check_user()
    async def announce(self, ctx: commands.Context[commands.Bot], hub: str):
        embed = discord.Embed(
            title='Hub Announcements',
            description='Create a **one time** announcement that sends in all connected channels. \n\n-# To create **recurring** announcements please visit the hub configuration menu.',
            color=self.constants.color(),
        )
        view = OpenView(self.bot, ctx.author, self.constants, hub)
        await ctx.send(embed=embed, view=view, ephemeral=True)

    @hub.command(name='rules', description='View the rules for a hub', extras={'category': 'Hubs'})
    @check_user()
    async def rules(self, ctx: commands.Context[commands.Bot], hub: Optional[str] = None):
        """Display hub rules to members."""
        # If no hub specified, try to get hub from current channel
        if not hub:
            if not ctx.guild:
                embed = discord.Embed(
                    title=t('common.titles.error', locale='en'),
                    description=t('commands.rules.errors.noHub', locale='en'),
                    color=discord.Color.red(),
                )
                await ctx.send(embed=embed, ephemeral=True)
                return

            # Get hub from current channel connection
            async with self.bot.db.get_session() as session:
                stmt = (
                    select(Connection, Hub)
                    .join(Hub, Connection.hubId == Hub.id)
                    .where(
                        Connection.channelId == str(ctx.channel.id), Connection.connected.is_(True)
                    )
                )
                result = await session.execute(stmt)
                connection_data = result.tuples().first()

                if not connection_data:
                    embed = discord.Embed(
                        title=t('common.titles.error', locale='en'),
                        description=t('commands.rules.errors.notConnected', locale='en'),
                        color=discord.Color.red(),
                    )
                    await ctx.send(embed=embed, ephemeral=True)
                    return

                _, hub_obj = connection_data
        else:
            # Get hub by name
            async with self.bot.db.get_session() as session:
                stmt = select(Hub).where(Hub.name == hub)
                hub_obj = await session.scalar(stmt)

                if not hub_obj:
                    embed = discord.Embed(
                        title=t('common.titles.error', locale='en'),
                        description=t(
                            'commands.rules.errors.hubNotFound', locale='en', hubName=hub
                        ),
                        color=discord.Color.red(),
                    )
                    await ctx.send(embed=embed, ephemeral=True)
                    return

        # Create rules display embed
        await self._display_hub_rules(ctx, hub_obj)

    async def _display_hub_rules(self, ctx: commands.Context[commands.Bot], hub: Hub):
        """Display hub rules in a formatted embed."""
        if not hub.rules or len(hub.rules) == 0:
            embed = discord.Embed(
                title=f'{self.bot.emotes.rules_icon} {t("commands.rules.title", locale="en", hubName=hub.name)}',
                description=t('commands.rules.noRules.description', locale='en'),
                color=self.constants.color(),
            )
        else:
            # Format rules as numbered list
            rules_text = ''
            for i, rule in enumerate(hub.rules, 1):
                rules_text += f'**{i}.** {rule}\n\n'

            # Truncate if too long for embed
            if len(rules_text) > 4000:
                rules_text = rules_text[:3950] + '...\n\n*Some rules truncated due to length.*'

            embed = discord.Embed(
                title=f'{self.bot.emotes.rules_icon} {t("commands.rules.title", locale="en", hubName=hub.name)}',
                description=rules_text,
                color=self.constants.color(),
            )

            plural = 's' if len(hub.rules) != 1 else ''
            embed.set_footer(
                text=t('commands.rules.footer', locale='en', count=len(hub.rules), plural=plural)
            )

        await ctx.send(embed=embed)


async def setup(bot: 'Bot'):
    await bot.add_cog(Hubs(bot))
