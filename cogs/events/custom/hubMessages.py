from typing import TYPE_CHECKING

from utils.modules.hub.hubLogging import (
    log_event,
    get_hub_log_config,
    get_log_channel_for_event,
    send_custom_log_to_hub,
    send_staff_log,
)
from utils.modules.hub.reportLogging import build_report_embed_and_view
from utils.modules.hub.appealLogging import build_appeal_embed_and_view
from utils.modules.events.baseEventCog import BaseEventCog
from utils.modules.events.eventDispatcher import (
    HubEvent,
    HubEventType,
)
from utils.constants import InterchatConstants, logger
from utils.modules.events.eventDecorator import hub_event_listener

if TYPE_CHECKING:
    from main import Bot


class HubMessageEvents(BaseEventCog):
    def __init__(self, bot: 'Bot'):
        self.bot = bot
        self.constants = InterchatConstants()
        super().__init__(bot)

    @hub_event_listener(HubEventType.MESSAGE_EDIT)
    async def on_message_edit(self, event: HubEvent):
        """Handle message edit events."""
        await log_event(self.bot, event)

    @hub_event_listener(HubEventType.MESSAGE_DELETE)
    async def on_message_delete(self, event: HubEvent):
        """Handle message deletion events."""
        await log_event(self.bot, event)

    @hub_event_listener(HubEventType.MESSAGE_REPORT)
    async def on_message_report(self, event: HubEvent):
        """Handle message report events."""
        scope = (event.extra_data or {}).get('scope')

        embed, view = build_report_embed_and_view(self.bot, event)

        if scope == 'global':
            sent = await send_staff_log(self.bot, embed, view)
            if not sent:
                logger.warning('Failed to send report to staff; no fallback available')
        if scope == 'hub':
            log_config = await get_hub_log_config(self.bot.db, event.hub_id)
            if not log_config:
                return

            channel_id = get_log_channel_for_event(HubEventType.MESSAGE_REPORT, log_config)
            if not channel_id:
                await send_custom_log_to_hub(
                    self.bot, event.hub_id, HubEventType.MESSAGE_REPORT, embed, view
                )

    @hub_event_listener(HubEventType.APPEAL_SUBMITTED)
    async def on_appeal_submitted(self, event: HubEvent):
        """Send appeal submissions to the Appeals log channel with action buttons."""
        embed, view = build_appeal_embed_and_view(self.bot, event)
        sent = await send_custom_log_to_hub(
            self.bot, event.hub_id, HubEventType.APPEAL_SUBMITTED, embed, view
        )
        if not sent:
            logger.debug('No appeals channel configured; appeal submission not logged')

    @hub_event_listener(HubEventType.NSFW_DETECTED)
    async def on_nsfw_detected(self, event: HubEvent):
        """Handle NSFW detection events."""
        await log_event(self.bot, event)


async def setup(bot):
    await bot.add_cog(HubMessageEvents(bot))
