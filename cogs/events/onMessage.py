import discord
from discord.ext import commands
from typing import TYPE_CHECKING

from utils.modules.broadcast.service import MessageBroadcastService

if TYPE_CHECKING:
    from main import Bot


class OnMessage(commands.Cog):
    def __init__(self, bot):
        self.bot: 'Bot' = bot

    @commands.Cog.listener()
    async def on_message(self, message: discord.Message):
        """Handle incoming messages for broadcasting."""
        async with self.bot.db.get_session() as session:
            broadcastsvc = MessageBroadcastService(self.bot, session)
            try:
                await broadcastsvc.process_message(message)
            finally:
                await broadcastsvc.close()


async def setup(bot):
    await bot.add_cog(OnMessage(bot))
