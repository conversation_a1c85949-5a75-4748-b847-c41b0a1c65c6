import discord
from discord.ext import commands

from utils.constants import InterchatConstants
from utils.modules.core.i18n import t


class OnGuildJoin(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.constants = InterchatConstants()

    @commands.Cog.listener()
    async def on_guild_join(self, guild: discord.Guild):
        # Use default 'en' on join; follow-up interactions will use user locale
        embed = discord.Embed(
            title=t('responses.welcome.onGuildJoinTitle', locale='en'),
            description=t(
                'responses.welcome.onGuildJoinDescription',
                locale='en',
                dot=self.bot.emotes.dot,
            ),
            color=self.constants.color(),
        )

        for channel in sorted(guild.text_channels, key=lambda x: x.position):
            everyone = channel.permissions_for(guild.default_role)
            bot = channel.permissions_for(guild.me)

            if everyone.send_messages and bot.send_messages:
                await channel.send(embed=embed)
                return


async def setup(bot):
    await bot.add_cog(OnGuildJoin(bot))
