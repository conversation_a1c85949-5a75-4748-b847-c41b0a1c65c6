import asyncio
import aiohttp
import discord
from discord.ext import commands
from sqlalchemy import select, delete
from typing import TYPE_CHECKING, Optional

from utils.modules.core.db.models import Broadcast, Connection
from utils.modules.core.cache import webhook_cache
from utils.constants import logger

if TYPE_CHECKING:
    from main import Bot


class onMessageDelete(commands.Cog):
    def __init__(self, bot: 'Bot'):
        self.bot = bot
        self._http_session = bot.http_session or aiohttp.ClientSession()
        # (channel_id, webhook_message_id, is_thread)
        self._delete_queue: asyncio.Queue[tuple[str, str, bool]] = asyncio.Queue()
        self._worker_task = bot.loop.create_task(self._delete_worker())

    async def cog_unload(self):
        self._worker_task.cancel()
        # Close only if we created our own session
        if not getattr(self.bot, 'http_session', None) and self._http_session:
            await self._http_session.close()

    @commands.Cog.listener()
    async def on_message_delete(self, message: discord.Message):
        if message.author.bot:
            return

        async with self.bot.db.get_session() as session:
            # Fetch only necessary columns (channelId and id)
            result = await session.execute(
                select(Broadcast.channelId, Broadcast.id, Connection.parentId)
                .join(Connection, Connection.channelId == Broadcast.channelId)
                .where(Broadcast.messageId == str(message.id))
            )
            records = result.all()

            if not records:
                return

            for rec in records:
                # If the connection has a parentId, this is a thread connection
                is_thread = bool(rec.parentId)
                self._delete_queue.put_nowait((rec.channelId, rec.id, is_thread))

            # Bulk delete database records
            await session.execute(delete(Broadcast).where(Broadcast.messageId == str(message.id)))
            await session.commit()
            logger.debug(f'Queued and removed {len(records)} broadcast record(s)')

    async def _get_webhook(self, channel_id: str) -> Optional[discord.Webhook]:
        """Get webhook from Redis cache or database."""
        # Check Redis cache first
        webhook_url = await webhook_cache.get_webhook_url(channel_id)

        if not webhook_url:
            # Fetch from database if not cached
            async with self.bot.db.get_session() as session:
                webhook_url = await session.scalar(
                    select(Connection.webhookURL).where(Connection.channelId == channel_id)
                )

            if webhook_url:
                # Cache the webhook URL
                await webhook_cache.set_webhook_url(channel_id, webhook_url)

        if webhook_url:
            return discord.Webhook.from_url(webhook_url, session=self._http_session)

        return None

    async def _delete_worker(self):
        while True:
            try:
                channel_id, msg_id, is_thread = await self._delete_queue.get()

                try:
                    webhook = await self._get_webhook(channel_id)

                    if not webhook:
                        logger.warning(f'No webhook for channel {channel_id}')
                        continue

                    try:
                        if is_thread:
                            await webhook.delete_message(
                                int(msg_id),
                                thread=discord.Object(id=int(channel_id)),
                            )
                        else:
                            await webhook.delete_message(int(msg_id))
                        logger.debug(f'Deleted webhook message {msg_id} in channel {channel_id}')
                    except discord.NotFound:
                        logger.warning(f'Message {msg_id} not found in channel {channel_id}')
                        # Clear cache if webhook is invalid
                        await webhook_cache.clear_webhook_url(channel_id)
                except discord.NotFound as e:
                    logger.warning(f'Webhook for channel {channel_id} not found: {e}')
                except Exception as e:
                    logger.error(f'Error deleting message {msg_id} in channel {channel_id}: {e}')
                finally:
                    # Always mark task as done since we successfully got it from queue
                    self._delete_queue.task_done()

            except asyncio.CancelledError:
                break


async def setup(bot: 'Bot'):
    await bot.add_cog(onMessageDelete(bot))
