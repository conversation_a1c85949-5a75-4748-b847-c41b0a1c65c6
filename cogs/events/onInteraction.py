import discord
from discord.ext import commands

from utils.modules.services.reactionService import ReactionService

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from main import Bot


class OnInteraction(commands.Cog):
    def __init__(self, bot: 'Bot'):
        self.bot = bot
        self.reaction_service: 'ReactionService'
        self.bot.loop.create_task(self.setup())

    async def setup(self):
        async with self.bot.db.get_session() as session:
            self.reaction_service = ReactionService(session)

    @commands.Cog.listener()
    async def on_interaction(self, interaction: discord.Interaction['Bot']):
        if (
            interaction.type != discord.InteractionType.component
            or interaction.data is None
            or interaction.data.get('component_type') != discord.ComponentType.button
        ):
            return

        custom_id: str = interaction.data.get('custom_id', '')
        if not custom_id.startswith('reaction_'):
            return

        if not interaction.message:
            return

        await interaction.response.defer()
        _, message_id, emoji_id = custom_id.split('_', 2)

        try:
            emoji = interaction.client.get_emoji(int(emoji_id))
            if emoji is None:
                emoji = emoji_id

        except ValueError:
            emoji = emoji_id

        await self.reaction_service.update_reacted(
            self.bot, interaction.message, emoji, interaction.user
        )
        await self.reaction_service.update_button_content(self.bot, interaction.message, emoji)


async def setup(bot):
    await bot.add_cog(OnInteraction(bot))
