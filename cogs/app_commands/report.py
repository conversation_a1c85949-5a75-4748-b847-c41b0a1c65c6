# TODO: Fix.

import discord
from discord.ext import commands
from discord import app_commands
from discord.ui import View, button, TextInput

from sqlalchemy import select
from typing import TYPE_CHECKING
from datetime import datetime, timedelta

from utils.modules.core.db.models import Message, Hub, HubReport as DbReport
from utils.constants import logger
from utils.modules.core.moderation import fetch_original_msg_with_extra
from utils.modules.ui.CustomModal import CustomModal
from utils.modules.core.i18n import t
from utils.utils import load_user_locale
from utils.modules.events.eventDispatcher import (
    create_hub_event,
    HubEventType,
    event_dispatcher,
)

if TYPE_CHECKING:
    from main import Bot


class ScopeView(View):
    def __init__(
        self,
        bot: 'Bot',
        user: discord.User | discord.Member,
        message: discord.Message,
        user_locale: str,
    ):
        super().__init__(timeout=120)
        self.bot: 'Bot' = bot
        self.user: discord.User | discord.Member = user
        self.constants = bot.constants
        self.message: discord.Message = message
        self.user_locale = user_locale
        self.setup_buttons()

    def setup_buttons(self): 
        self.global_callback.label = t('ui.report.buttons.toStaff', locale=self.user_locale) 
        self.global_callback.emoji = self.bot.emotes.globe_icon 
        self.global_callback.disabled = False

        self.hub_callback.label = t('ui.report.buttons.toHub', locale=self.user_locale) 
        self.hub_callback.emoji = self.bot.emotes.house_icon 
        self.hub_callback.disabled = False

    async def _fetch_messages(self):
        async with self.bot.db.get_session() as session:
            ten_minutes_ago = datetime.now() - timedelta(minutes=10)

            stmt = (
                select(Message)
                .where(
                    Message.channelId == str(self.message.channel.id),
                    Message.createdAt >= ten_minutes_ago,
                )
                .order_by(Message.createdAt.desc())
            )
            result = await session.execute(stmt)
            messages = result.scalars().all()

            if not messages:
                stmt = (
                    select(Message)
                    .where(Message.channelId == str(self.message.channel.id))
                    .order_by(Message.createdAt.desc())
                    .limit(50)
                )
                result = await session.execute(stmt)
                messages = result.scalars().all()

            return messages

    async def _open_reason_modal(self, interaction: discord.Interaction, title: str) -> str | None:
        modal = CustomModal(
            title,
            [
                (
                    'reason',
                    TextInput(
                        label=t('ui.report.modal.reason.label', locale=self.user_locale),
                        placeholder=t('ui.report.modal.reason.placeholder', locale=self.user_locale),
                        required=True,
                        max_length=500,
                        style=discord.TextStyle.paragraph,
                    ),
                )
            ],
        )
        await interaction.response.send_modal(modal)
        if not await modal.wait():
            return modal.saved_items['reason'].value.strip()
        return None

    async def _dispatch_report_event(
        self,
        interaction: discord.Interaction,
        scope: str,
        reason: str,
    ):
        async with self.bot.db.get_session() as session:
            fetched = await fetch_original_msg_with_extra(session, str(self.message.id))
            if not fetched:
                logger.error(f'Failed to fetch original message for report: {self.message.id}')
                return

            db_message, _db_author_user, hub_id = fetched
            original_content = self.message.content or db_message.content or ''

            hub_rec = await session.get(Hub, hub_id)
            hub_name = hub_rec.name if hub_rec else 'Unknown Hub'

            report = DbReport(
                hubId=str(hub_id),
                reporterId=str(self.user.id),
                reportedServerId=str(self.message.guild.id) if self.message.guild else '',
                reportedUserId=_db_author_user.id,
                messageId=db_message.id,
                reason=reason,
            )
            session.add(report)
            await session.commit()

        # Prepare attachments (safe outside session)
        attachment_meta = [
            {
                'url': a.url,
                'filename': a.filename,
                'content_type': a.content_type,
                'size': a.size,
            }
            for a in getattr(self.message, 'attachments', [])
        ]

        target_guild_id = str(self.message.guild.id) if self.message.guild else ''
        target_guild_name = self.message.guild.name if self.message.guild else 'Direct Message'

        event = create_hub_event(
            event_type=HubEventType.MESSAGE_REPORT,
            hub_id=str(hub_id),
            hub_name=str(hub_name),
            moderator_id=str(self.user.id),
            moderator_name=str(self.user),
            target_user_id=str(self.message.author.id) if self.message.author else None,
            target_user_name=str(
                getattr(self.message.author, 'name', None)
                or getattr(self.message.author, 'display_name', None)
                or 'Unknown'
            )
            if self.message.author else None,
            target_server_id=target_guild_id,
            target_server_name=target_guild_name,
            message_id=str(self.message.id),
            channel_id=str(self.message.channel.id),
            original_content=original_content,
            report_id=report.id,
            reason=reason,
            extra_data={'scope': scope, 'attachments': attachment_meta},
        )
        await event_dispatcher.dispatch_hub_event(event)

    @button(label='Report to Staff', style=discord.ButtonStyle.grey, disabled=False)
    async def global_callback(self, interaction: discord.Interaction, _button: discord.ui.Button):
        reason = await self._open_reason_modal(
            interaction, t('ui.report.modal.toStaff.title', locale=self.user_locale)
        )
        if not reason:
            return

        await self._dispatch_report_event(interaction, scope='global', reason=reason)
        embed = discord.Embed(
            title=t('commands.report.success.title', locale=self.user_locale),
            description=t(
                'commands.report.success.toStaff', locale=self.user_locale, tick=self.bot.emotes.tick
            ),
            color=self.constants.color(),
        )
        await interaction.followup.send(embed=embed, ephemeral=True)

    @button(label='Report to Hub', style=discord.ButtonStyle.grey, disabled=False)
    async def hub_callback(self, interaction: discord.Interaction, _button: discord.ui.Button):
        reason = await self._open_reason_modal(
            interaction, t('ui.report.modal.toHub.title', locale=self.user_locale)
        )
        if not reason:
            return

        await self._dispatch_report_event(interaction, scope='hub', reason=reason)
        embed = discord.Embed(
            title=t('commands.report.success.title', locale=self.user_locale),
            description=t(
                'commands.report.success.toHub', locale=self.user_locale, tick=self.bot.emotes.tick
            ),
            color=self.constants.color(),
        )
        await interaction.followup.send(embed=embed, ephemeral=True)


class Report(commands.Cog):
    def __init__(self, bot: 'Bot'):
        self.bot = bot
        self.constants = bot.constants

        # Register context menu
        self.report_message_menu = app_commands.ContextMenu(
            name=t('commands.report.contextMenu', locale='en'),
            callback=self.report_message
        )
        self.bot.tree.add_command(self.report_message_menu)

    async def cog_unload(self):
        # Remove context menu on unload
        self.bot.tree.remove_command(
            self.report_message_menu.name,
            type=discord.AppCommandType.message
        )

    async def report_message(self, interaction: discord.Interaction, message: discord.Message):
        try:
            await interaction.response.defer(ephemeral=True)

            async with self.bot.db.get_session() as session:
                result = await fetch_original_msg_with_extra(session, str(message.id))
                user_locale = await load_user_locale(self.bot, interaction)

            if not result:
                embed = discord.Embed(
                    title=t('ui.common.titles.error', locale=user_locale),
                    description=t(
                        'commands.report.errors.hubMessageOnly',
                        locale=user_locale,
                        x_icon=self.bot.emotes.x_icon,
                    ),
                    color=self.constants.color(),
                )
                return await interaction.followup.send(embed=embed, ephemeral=True)

            db_message, reported_user, _hub_id = result

            if int(reported_user.id) == interaction.user.id:
                embed = discord.Embed(
                    title=t('ui.common.titles.error', locale=user_locale),
                    description=t(
                        'commands.report.errors.cannotReportSelf',
                        locale=user_locale,
                        x_icon=self.bot.emotes.x_icon,
                    ),
                    color=self.constants.color(),
                )
                return await interaction.followup.send(embed=embed, ephemeral=True)

            original_content = db_message.content or message.content or ""
            embed = discord.Embed(
                title=t('commands.report.title', locale=user_locale),
                description=t('commands.report.description', locale=user_locale, user=f'<@{int(reported_user.id)}>'),
                color=self.constants.color(),
            )
            embed.set_author(name=f'@{interaction.user}', icon_url=interaction.user.display_avatar.url)
            embed.set_footer(text=t('commands.report.footer', locale=user_locale))

            view = ScopeView(self.bot, interaction.user, db_message, user_locale)
            await interaction.followup.send(embed=embed, view=view, ephemeral=True)
        except Exception as e:
            print(e)

async def setup(bot: 'Bot'):
    await bot.add_cog(Report(bot))
