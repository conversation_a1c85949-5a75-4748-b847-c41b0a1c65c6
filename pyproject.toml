[project]
name = "interchat"
version = "5.0.0-dev"
description = "InterChat is a discord bot that lets you talk across servers"
readme = "README.md"
license = { file = "LICENCE" }
requires-python = ">=3.12"
dependencies = [
  "aiofiles>=24.1.0",
  "aiohttp>=3.12.15",
  "alembic>=1.16.4",
  "asyncpg>=0.30.0",
  "cogwatch>=3.3.1",
  "colorlog>=6.9.0",
  "discord>=2.3.2",
  "dotenv>=0.9.9",
  "jinja2>=3.1.6",
  "jishaku>=2.6.0",
  "playwright>=1.54.0",
  "cuid2>=2.0.1",
  "psutil>=7.0.0",
  "psycopg-binary>=3.2.9",
  "pyright>=1.1.403",
  "pyyaml>=6.0.2",
  "redis>=6.4.0",
  "requests>=2.32.5",
  "sentry-sdk>=2.35.0",
  "six>=1.17.0",
  "zuid>=0.1.1",
]

[tool.ruff]
line-length = 100

[tool.ruff.format]
quote-style = "single"

[dependency-groups]
dev = [
  "psycopg2-binary>=2.9.10",
  "pytest>=8.4.1",
  "ruff>=0.12.9",
  "ty>=0.0.1a19",
  "watchdog>=6.0.0",
]
