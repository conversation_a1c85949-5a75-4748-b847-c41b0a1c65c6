import re
from typing import TYPE_CHECKING, List, Optional, Tuple, Dict
from urllib.parse import urlparse

import discord
from sqlalchemy import and_, select

from utils.modules.core.db.models import Broadcast, Hub, Message, User

if TYPE_CHECKING:
    from sqlalchemy.ext.asyncio import AsyncSession

    from main import Bot
    from utils.modules.services.userService import UserService


# Compile regex patterns once at module level for better performance
URL_PATTERN = re.compile(r'https?://[^\s]+')
IMAGE_URL_PATTERN = re.compile(r'\[⁥\]\(([^)]+)\)')

# Precompile common URL checks for performance
IMAGE_EXTENSIONS = frozenset(['.png', '.webp', '.jpg', '.jpeg'])
GIF_EXTENSION = '.gif'
TENOR_DOMAIN = 'tenor.com'


def is_image_url(url: str) -> bool:
    """Check if URL is an image (png, webp, jpg, jpeg)"""
    try:
        parsed_url = urlparse(url.lower())
        return any(parsed_url.path.endswith(ext) for ext in IMAGE_EXTENSIONS)
    except (ValueError, AttributeError):
        return False


def is_gif_url(url: str) -> bool:
    """Check if URL is a GIF"""
    try:
        parsed_url = urlparse(url.lower())
        return parsed_url.path.endswith(GIF_EXTENSION)
    except (ValueError, AttributeError):
        return False


def is_tenor_gif(url: str) -> bool:
    """Check if URL is from Tenor"""
    return TENOR_DOMAIN in url.lower()


def format_message_with_attachments(
    content: str,
    attachments: List[discord.Attachment],
    stickers: List[discord.StickerItem],
) -> tuple[str, list]:
    """Process message content and add attachment/sticker URLs"""
    if not content:
        content = ''

    _content = content
    urls = []

    # Process attachments
    for attachment in attachments:
        if is_image_url(attachment.url) or is_gif_url(attachment.url):
            urls.append(attachment.url)
            # Add image/GIF with special format
            _content += f'\n[⁥]({attachment.url})'

    # Process stickers
    for sticker in stickers:
        _content += f'\n[⁥]({sticker.url})'

    # Check for image/GIF URLs in content - use precompiled regex
    for url in URL_PATTERN.findall(_content):
        try:
            if is_gif_url(url) and not is_tenor_gif(url):
                # Remove non-Tenor GIF URLs from content
                _content = _content.replace(url, '[GIF blocked - only Tenor GIFs allowed]')
            urls.append(url)
        except Exception:
            # Skip malformed URLs
            continue

    return _content, urls


async def format_user_badges(bot: 'Bot', user_id: int, usersvc: 'UserService') -> str:
    user_data = await usersvc.fetch_badges(bot, str(user_id))
    show_badges, user_badges = user_data

    if not show_badges:
        return ''

    badge_emojis = [badge['icon'] for badge in user_badges]

    if badge_emojis:
        return f'{" ".join(badge_emojis)}'

    return ''


def create_allowed_mentions_for_broadcast(
    replied_user_id: Optional[int],
) -> discord.AllowedMentions:
    """
    Create AllowedMentions object for broadcasts.
    Only allows mentions for replies going to the original server.
    """
    # Block all mentions by default
    allowed_mentions = discord.AllowedMentions.none()

    # Only allow user mention if this is a reply going to the original server
    if replied_user_id:
        allowed_mentions.users = [discord.Object(id=replied_user_id)]

    return allowed_mentions


def is_reply_mention_required(target_server_id: str, original_server_id: Optional[str]) -> bool:
    """Determine if reply mention should be included based on configuration.
    This can be extended to check bot settings or user preferences.
    """
    if original_server_id and target_server_id == original_server_id:
        return True
    return False


async def create_reply_embed(
    message_reference: discord.MessageReference | None,
    reply_data: Optional[Tuple[Message, User, str]],
    target_channel_id: str,
    target_server_id: str,
    broadcast_message_id: Optional[str] = None,
) -> Optional[discord.Embed]:
    """Create reply embed if message is a reply"""
    if not message_reference or not message_reference.message_id:
        return None

    if reply_data:
        original_message, author, _ = reply_data

        embed = create_embed_from_message_and_author(original_message, author)

        if broadcast_message_id:
            jump_link = build_discord_jump_link(
                target_server_id,
                target_channel_id,
                broadcast_message_id,
            )
            prefix = f'[**Reply To**]({jump_link})'
            embed.description = f'{prefix}: {embed.description or ""}'

        return embed

    return None


def create_embed_from_message_and_author(message: 'Message', author: 'User') -> discord.Embed:
    """Helper method to create embed from message and author"""
    try:
        # Safely handle None values and truncate username to 30 characters
        username = (author.name or 'Unknown User')[:30]

        # Safely handle None content and truncate to 100 characters
        content = message.content or ''
        if len(content) > 100:
            content = content[:100] + '...'

        embed = discord.Embed(
            description=content,
            color=discord.Colour.random(),
        )

        author_image = getattr(author, 'image', None)

        if author_image:
            embed.set_author(name=username, icon_url=author_image)

        return embed
    except Exception:
        return discord.Embed(description='Error loading reply preview', color=discord.Colour.red())


def build_discord_jump_link(guild_id: str, channel_id: str, message_id: str) -> str:
    """Build a Discord jump link for a message."""
    return f'https://discord.com/channels/{guild_id}/{channel_id}/{message_id}'


async def get_broadcast_ids_for_channels(
    session: 'AsyncSession', original_message_id: str, channel_ids: List[str]
) -> Dict[str, str]:
    """Batch fetch broadcast message IDs for an original message across many channels.

    Returns a mapping of channelId -> broadcastId.
    """
    if not channel_ids:
        return {}

    stmt = select(Broadcast.channelId, Broadcast.id).where(
        and_(Broadcast.messageId == original_message_id, Broadcast.channelId.in_(channel_ids))
    )
    result = await session.execute(stmt)
    rows = result.tuples()

    mapping: Dict[str, str] = {}
    for channel_id, broadcast_id in rows:
        if channel_id and broadcast_id:
            mapping[channel_id] = broadcast_id
    return mapping


async def store_message_and_broadcasts(
    message: discord.Message,
    hub: Hub,
    processed_content: str,
    broadcast_message_ids: List[Tuple[str, str]],
    session: 'AsyncSession',
    referred_message: Optional[Message] = None,
) -> None:
    try:
        # Extract image URL from processed content if present
        image_url = None
        if '[⁥](' in processed_content:
            match = IMAGE_URL_PATTERN.search(processed_content)
            if match:
                image_url = match.group(1)

        # Safely handle guild ID
        guild_id = str(message.guild.id) if message.guild else ''

        # Safely handle message reference
        referred_message_id = referred_message.id if referred_message else None

        # Create message object
        message_obj = Message(
            id=str(message.id),
            hubId=hub.id,
            content=processed_content,
            imageUrl=image_url,
            channelId=str(message.channel.id),
            guildId=guild_id,
            authorId=str(message.author.id),
            createdAt=message.created_at.replace(tzinfo=None),
            referredMessageId=referred_message_id,
        )

        # Use merge for upsert behavior (handles conflicts automatically)
        session.add(message_obj)

        # Create broadcast objects if any exist
        if broadcast_message_ids:
            broadcast_objects = [
                Broadcast(
                    id=broadcast_id,
                    messageId=str(message.id),
                    channelId=channel_id,
                )
                for broadcast_id, channel_id in broadcast_message_ids
            ]

            # Add all broadcast objects to session
            session.add_all(broadcast_objects)

        await session.commit()

    except Exception as e:
        await session.rollback()
        raise RuntimeError(f'Failed to store message and broadcasts: {e}') from e


def validate_discord_id(discord_id: str) -> bool:
    """Validate that a Discord ID is a valid snowflake"""
    try:
        id_int = int(discord_id)
        # Discord snowflakes are 64-bit integers
        return 0 < id_int < (1 << 64)
    except (ValueError, TypeError):
        return False


def truncate_text(text: str, max_length: int, suffix: str = '...') -> str:
    """Safely truncate text with proper suffix handling"""
    if not text:
        return ''

    if len(text) <= max_length:
        return text

    return text[: max_length - len(suffix)] + suffix
