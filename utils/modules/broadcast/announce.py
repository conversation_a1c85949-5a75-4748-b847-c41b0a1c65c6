import discord
from sqlalchemy import select
from typing import TYPE_CHECKING
from utils.modules.core.db.models import Connection

if TYPE_CHECKING:
    from main import Bot


async def broadcast_announcement(bot: 'Bot', hub: dict, embed: discord.Embed):
    async with bot.db.get_session() as session:
        stmt = select(Connection.webhookURL).where(Connection.hubId == hub['id'])
        result = (await session.scalars(stmt)).all()

    for url in result:
        webhook = discord.Webhook.from_url(url, client=bot)
        await webhook.send(embed=embed, username=f'{hub["name"]} | Offical Hub Announcement')
