from typing import TYPE_CHECKING, List, Optional, Tuple
import asyncio

import aiohttp
import discord
from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession

from utils.constants import logger

if TYPE_CHECKING:
    from utils.modules.broadcast.checks import ValidationResult
from utils.modules.broadcast.messageUtils import (
    create_allowed_mentions_for_broadcast,
    create_reply_embed,
    format_message_with_attachments,
    format_user_badges,
    is_reply_mention_required,
    get_broadcast_ids_for_channels,
    store_message_and_broadcasts,
)
from utils.modules.broadcast.notify import NotificationManager
from utils.modules.core.antiSpam import AntiSpamManager
from utils.modules.core.db.models import Connection, Hub, HubRulesAcceptance, Message, User
from utils.modules.core.moderation import fetch_original_msg_with_extra
from utils.modules.services.hubService import HubService
from utils.modules.services.userService import UserService
from utils.modules.services.validationService import ValidationService
from utils.utils import upsert_user
from utils.constants import constants

if TYPE_CHECKING:
    from main import Bot


class MessageBroadcastService:
    """Service for handling message broadcasting operations."""

    def __init__(self, bot: 'Bot', session: 'AsyncSession'):
        self.bot = bot
        self.session = session
        self._http_session = bot.http_session or aiohttp.ClientSession()

        # Initialize service dependencies
        self.hubsvc = HubService(session)
        self.usersvc = UserService(session)
        self.validationsvc = ValidationService(session)

    async def close(self):
        """Clean up resources when using a local session."""
        # Only close if we created our own session
        if not getattr(self.bot, 'http_session', None) and self._http_session:
            await self._http_session.close()

    async def process_message(self, message: discord.Message) -> bool:
        """
        Process a message for broadcasting.
        Returns True if message was processed, False if it should be ignored.
        """
        # Basic validation
        if message.author.bot or message.author.system or not message.guild:
            return False

        # Check if message is from a connected channel
        connection_data = await self._get_connection_and_hub(
            str(message.channel.id), str(message.author.id)
        )
        if not connection_data:
            return False

        connection, hub, rules_acceptance = connection_data
        logger.debug(f'Message from {message.author}: {message.content}')

        # Upsert user data
        await upsert_user(message.author, self.session)

        # Check if this is user's first message in hub and show welcome rules
        await self._check_and_show_welcome_rules(message, hub, rules_acceptance)

        # Validate message
        validation_result = await self.validationsvc.validate_message_for_broadcast(message, hub)
        if not validation_result.is_valid:
            await self._handle_validation_failure(message, validation_result, hub)
            return False

        # Process and broadcast message
        await self._broadcast_message_to_hub(message, connection, hub)
        return True

    async def _get_connection_and_hub(
        self, channel_id: str, user_id: str
    ) -> Optional[Tuple[Connection, Hub, HubRulesAcceptance]]:
        """Get connection and hub data for a channel."""
        stmt = (
            select(Connection, Hub, HubRulesAcceptance)
            .select_from(Connection)
            .join(Hub, Connection.hubId == Hub.id)  # Hub must exist if Connection exists
            .join(
                HubRulesAcceptance,
                and_(
                    HubRulesAcceptance.hubId == Hub.id,
                    HubRulesAcceptance.userId == user_id,
                ),
                isouter=True,
            )
            .where(
                Connection.channelId == channel_id,
                Connection.connected.is_(True),
            )
            .limit(1)
        )

        result = await self.session.execute(stmt)
        return result.tuples().first()

    async def _handle_validation_failure(
        self, message: discord.Message, validation_result: 'ValidationResult', hub: Hub
    ):
        """Handle validation failure with appropriate actions."""
        # Handle spam-specific actions
        if validation_result.spam_action:
            spam_manager = AntiSpamManager(self.session)

            if validation_result.spam_action == 'mute':
                # Mute the user for spam
                infraction_id = await spam_manager.mute_user_for_spam(
                    self.bot, str(message.author.id), hub.id, message.author.name
                )
                if infraction_id:
                    # Update notification message with infraction ID for tracking
                    validation_result = validation_result._replace(infraction_id=infraction_id)

        # Send notification if needed
        if validation_result.should_notify and validation_result.notification_message:
            notifier = NotificationManager(self.session)
            await notifier.send_validation_notification(
                message.author,
                validation_result.notification_message,
                validation_result.infraction_id,
            )

        logger.info(f'Message blocked: {validation_result.reason}')

    async def _broadcast_message_to_hub(
        self, message: discord.Message, connection: Connection, hub: Hub
    ):
        """Broadcast message to all connections in the hub."""
        # Process message content
        processed_content, attachment_urls = format_message_with_attachments(
            message.content, message.attachments, message.stickers
        )

        # Get other connections in the hub
        other_connections = await self._get_other_connections(hub.id, connection.channelId)
        if not other_connections:
            logger.debug(f'No other connections found for hub {hub.id}')
            return

        # Get reply data and user badges
        reply_data = await self._get_reply_data(message)
        badge_prefix = await format_user_badges(self.bot, message.author.id, self.usersvc)
        # Precompute broadcast IDs for the replied message across all target channels (if this is a reply)
        channel_to_broadcast_id: dict[str, str] = {}
        if reply_data:
            original_message, _, _ = reply_data
            channel_to_broadcast_id = await get_broadcast_ids_for_channels(
                self.session, original_message.id, [c.channelId for c in other_connections]
            )

        # Broadcast to other connections
        broadcast_message_ids = await self._send_broadcasts(
            message,
            other_connections,
            processed_content,
            badge_prefix,
            reply_data,
            channel_to_broadcast_id,
            attachment_urls,
        )

        # Store message and broadcast records
        await store_message_and_broadcasts(
            message,
            hub,
            processed_content,
            broadcast_message_ids,
            self.session,
            referred_message=reply_data[0] if reply_data else None,
        )

    async def _get_other_connections(
        self, hub_id: str, exclude_channel_id: str
    ) -> List[Connection]:
        """Get all other connections in the hub."""
        stmt = select(Connection).where(
            Connection.hubId == hub_id,
            Connection.connected,
            Connection.channelId != exclude_channel_id,
        )
        result = await self.session.execute(stmt)
        return list(result.scalars().all())

    async def _get_reply_data(self, message: discord.Message):
        """Get reply data if this message is a reply."""
        if message.reference and message.reference.message_id:
            return await fetch_original_msg_with_extra(
                self.session, str(message.reference.message_id)
            )

    async def _send_broadcasts(
        self,
        message: discord.Message,
        connections: List[Connection],
        processed_content: str,
        badge_prefix: str,
        reply_data: Optional[Tuple[Message, User, str]],
        channel_to_broadcast_id: dict[str, str],
        attachment_urls: list[str],
    ) -> List[Tuple[str, str]]:
        """Send broadcasts to all connections."""
        broadcast_message_ids = []

        # Extract reply information for reply mentions
        replied_user_id = None
        original_server_id = None
        if reply_data:
            replied_message, replied_user, _ = reply_data
            replied_user_id = replied_user.id
            original_server_id = replied_message.guildId

        semaphore = asyncio.Semaphore(20)

        async def send_one(conn: Connection):
            async with semaphore:
                try:
                    sent_message = await self._send_single_broadcast(
                        message,
                        conn,
                        processed_content,
                        badge_prefix,
                        replied_user_id,
                        original_server_id,
                        reply_data,
                        channel_to_broadcast_id,
                        attachment_urls,
                    )
                    if sent_message:
                        return (str(sent_message.id), conn.channelId)
                except Exception as e:
                    logger.error(f'Failed to broadcast to channel {conn.channelId}: {e}')
                return None

        results = await asyncio.gather(*(send_one(c) for c in connections), return_exceptions=False)
        for item in results:
            if item:
                broadcast_message_ids.append(item)

        logger.debug(
            f'Broadcasted message from {message.author} to {len(broadcast_message_ids)} channels'
        )

        return broadcast_message_ids

    async def _send_single_broadcast(
        self,
        message: discord.Message,
        connection: Connection,
        processed_content: str,
        badge_prefix: str,
        replied_user_id: Optional[str],
        original_server_id: Optional[str],
        reply_data: Optional[Tuple[Message, User, str]],
        channel_to_broadcast_id: dict[str, str],
        attachment_urls: list[str],
    ) -> Optional[discord.WebhookMessage]:
        """Send a single broadcast message."""
        webhook = discord.Webhook.from_url(url=connection.webhookURL, session=self._http_session)

        should_mention = bool(
            replied_user_id and is_reply_mention_required(connection.serverId, original_server_id)
        )

        logger.debug(
            f'Sending broadcast to {connection.channelId}; mention_required={should_mention}'
        )

        # Create allowed mentions for this specific broadcast
        allowed_mentions = create_allowed_mentions_for_broadcast(
            int(replied_user_id) if should_mention and replied_user_id else None
        )

        # Prepare embeds and content
        reply_embed: Optional[discord.Embed] = None
        try:
            # Use precomputed broadcast id if available for the target channel
            precomputed_broadcast_id = channel_to_broadcast_id.get(connection.channelId)
            if not attachment_urls:
                reply_embed = await create_reply_embed(
                    message.reference,
                    reply_data,
                    connection.channelId,
                    connection.serverId,
                    # use broadcast ID if available; otherwise, send it to the server that originated the replied-to message
                    precomputed_broadcast_id or reply_data[0].id if reply_data else None,
                )
        except Exception:
            reply_embed = None

        embeds = [reply_embed] if reply_embed else []
        reply_mention = f'<@{replied_user_id}> ' if should_mention else ''
        final_content = f'{reply_mention}{processed_content}\n-# {badge_prefix}'

        # Send message (handle threads vs regular channels)
        if connection.parentId:
            return await webhook.send(
                final_content,
                username=f'{message.author.name} | {getattr(message.guild, "name", "Unknown Server")}',
                avatar_url=message.author.display_avatar.url,
                thread=discord.Object(id=connection.channelId),
                embeds=embeds,
                allowed_mentions=allowed_mentions,
                wait=True,
            )
        else:
            return await webhook.send(
                final_content,
                username=f'{message.author.name} | {getattr(message.guild, "name", "Unknown Server")}',
                avatar_url=message.author.display_avatar.url,
                embeds=embeds,
                allowed_mentions=allowed_mentions,
                wait=True,
            )

    async def _check_and_show_welcome_rules(
        self, message: discord.Message, hub: Hub, rules_acceptance: HubRulesAcceptance
    ):
        """Check if user is new to hub and show welcome rules if configured."""
        # Only show rules if hub has rules configured
        if not hub.rules or len(hub.rules) == 0:
            return

        # Check if user has already seen rules for this hub
        existing_acceptance = (
            rules_acceptance.userId == str(message.author.id) if rules_acceptance else None
        )

        if existing_acceptance:
            return

        # Create rules acceptance record
        rules_acceptance = HubRulesAcceptance(userId=str(message.author.id), hubId=hub.id)
        self.session.add(rules_acceptance)
        await self.session.commit()

        # Send welcome rules message
        await self._send_welcome_rules_message(message, hub)

    # TODO: Add buttons to accept deny...
    async def _send_welcome_rules_message(self, message: discord.Message, hub: Hub):
        """Send a welcome message with hub rules to the user."""
        # Format rules as numbered list
        rules_text = ''
        for i, rule in enumerate(hub.rules, 1):
            rules_text += f'**{i}.** {rule}\n'

        # Truncate if too long for embed
        if len(rules_text) > 4000:
            rules_text = rules_text[:3950] + '...\n\n*Some rules truncated due to length.*'

        embed = discord.Embed(
            title=f'Welcome to {hub.name}! 📋',
            description=f'Hi {message.author.mention}! Welcome to the InterChat hub, **{hub.name}**. Please take a moment to read our community rules:\n\n{rules_text}',
            color=constants.color(),  # Discord blurple
        )

        plural = 's' if len(hub.rules) != 1 else ''
        embed.set_footer(
            text=f'{len(hub.rules)} rule{plural} • Follow these guidelines to maintain a positive community'
        )

        try:
            await message.channel.send(embed=embed)
        except discord.HTTPException:
            logger.error(
                f'Failed to send welcome rules message for hub {hub.id} to user {message.author.id}'
            )
