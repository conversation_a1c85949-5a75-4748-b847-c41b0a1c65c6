from typing import TYPE_CHECKING

import discord

from utils.modules.events.eventDispatcher import <PERSON>bEvent
from utils.modules.ui.views.ModerationViews import ReportActionView

if TYPE_CHECKING:
    from main import Bo<PERSON>


def _is_image(attachment: dict) -> bool:
    content_type = (attachment.get('content_type') or '').lower()
    return content_type.startswith('image/')


def build_report_embed_and_view(
    bot: 'Bot', event: HubEvent
) -> tuple[discord.Embed, ReportActionView]:
    if not event.report_id:
        raise ValueError('Report ID is required to build report embed')

    emotes = bot.emotes

    # Title and color
    embed = discord.Embed(
        title=f'{emotes.report_icon} Message Report',
        color=discord.Color.magenta(),
        timestamp=event.timestamp,
    )

    # Reporter context
    reporter_value = f'{event.moderator_name} (`{event.moderator_id}`)'
    embed.add_field(name='Reporter', value=reporter_value, inline=True)

    # Target context
    if event.target_user_id:
        embed.add_field(
            name='Reported User',
            value=f'<@{event.target_user_id}> (`{event.target_user_id}`)',
            inline=True,
        )
    if event.target_server_id:
        embed.add_field(
            name='Server',
            value=f'{event.target_server_name or "Unknown"} (`{event.target_server_id}`)',
            inline=True,
        )

    # Location
    if event.channel_id:
        embed.add_field(name='Channel', value=f'<#{event.channel_id}>', inline=True)

    # Reason
    if event.reason:
        embed.add_field(name='Reason', value=event.reason[:1024], inline=False)

    # Evidence: original content
    if event.original_content:
        display_content = (
            event.original_content[:1024] + '…'
            if len(event.original_content) > 1024
            else event.original_content
        )
        embed.add_field(name='Message Content', value=display_content or '*none*', inline=False)

    # Evidence: attachments
    attachments: list[dict] = (event.extra_data or {}).get('attachments') or []
    if attachments:
        # Show first image inline if available
        first_image = next((a for a in attachments if _is_image(a)), None)
        if first_image:
            embed.set_image(url=first_image.get('url'))

        # Add attachment links
        links = []
        for idx, a in enumerate(attachments[:10], start=1):
            url = a.get('url')
            filename = a.get('filename') or f'attachment_{idx}'
            if not url:
                continue
            links.append(f'[`{filename}`]({url})')
        if links:
            embed.add_field(name='Attachments', value=' '.join(links), inline=False)

    # Jump link (guild channels only)
    if event.target_server_id and event.channel_id and event.message_id:
        jump = f'https://discord.com/channels/{event.target_server_id}/{event.channel_id}/{event.message_id}'
        embed.add_field(name='Jump to Message', value=f'[Open]({jump})', inline=False)

    embed.set_footer(text=f'Hub: {event.hub_name} (`{event.hub_id}`)')

    view = ReportActionView(
        bot=bot,
        report_id=event.report_id,
        locale='en',
    )

    return embed, view
