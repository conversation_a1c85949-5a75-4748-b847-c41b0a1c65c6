from typing import TYPE_CHECKING, Optional, Union
import discord
from sqlalchemy import select

from utils.modules.core.db.models import HubLogConfig
from utils.modules.emojis.EmojiManager import EmojiManager
from utils.modules.events.eventDispatcher import <PERSON>bEvent, HubEventType
from utils.constants import logger

if TYPE_CHECKING:
    from main import Bot
    from utils.modules.core.db.database import Database

MessageableChannel = Union[
    discord.TextChannel,
    discord.VoiceChannel,
    discord.StageChannel,
    discord.Thread,
    discord.DMChannel,
]


async def log_event(bot: 'Bot', event: HubEvent):
    """
    Core method to handle logging any hub event to the appropriate channel.
    """
    log_config = await get_hub_log_config(bot.db, event.hub_id)
    if not log_config:
        logger.debug(f'No log configuration found for hub {event.hub_id}')
        return

    channel_id = get_log_channel_for_event(event.event_type, log_config)
    if not channel_id:
        logger.debug(f'No log channel configured for event type {event.event_type.value}')
        return

    channel_id_int = int(channel_id)
    channel = bot.get_channel(channel_id_int) or await bot.fetch_channel(channel_id_int)

    if not channel:
        logger.warning(f'Log channel {channel_id} not found or bot lacks access')
        return

    if not isinstance(
        channel,
        (
            discord.TextChannel,
            discord.VoiceChannel,
            discord.StageChannel,
            discord.Thread,
        ),
    ):
        logger.warning(f'Log channel {channel_id} is not a messageable channel')
        return

    bot_perms = channel.permissions_for(channel.guild.me)
    if not bot_perms.send_messages or not bot_perms.embed_links:
        logger.warning(f'Bot lacks permission to send messages in log channel {channel_id}')
        return

    # channel type
    if not isinstance(
        channel,
        (
            discord.TextChannel,
            discord.StageChannel,
            discord.VoiceChannel,
            discord.Thread,
        ),
    ):
        logger.warning(f'Log channel {channel_id} is not a text channel')
        return

    await send_log_message(channel, event, channel_id, bot.emotes)


async def send_log_message(
    channel: MessageableChannel, event: HubEvent, channel_id: str, emotes: 'EmojiManager'
):
    """
    Send the log message to the specified channel with fallback handling.
    """
    embed = create_log_embed(emotes, event)
    await channel.send(embed=embed)
    logger.debug(
        f'Logged {event.event_type.value} event for hub {event.hub_name} to channel {channel_id}'
    )


async def get_hub_log_config(db: 'Database', hub_id: str) -> Optional[HubLogConfig]:
    """
    Get the log configuration for a hub.
    """
    async with db.get_session() as session:
        stmt = select(HubLogConfig).where(HubLogConfig.hubId == hub_id)
        result = await session.execute(stmt)
        return result.scalar_one_or_none()


def get_log_channel_for_event(event_type: HubEventType, log_config: HubLogConfig) -> Optional[str]:
    """
    Determine which log channel to use for a specific event type.
    """

    # Map event types to log channels based on categories
    event_channel_mapping = {
        # Hub management events -> mod logs
        HubEventType.HUB_DELETE: log_config.modLogsChannelId,
        HubEventType.HUB_CREATE: log_config.modLogsChannelId,
        HubEventType.HUB_UPDATE: log_config.modLogsChannelId,
        # User moderation events -> mod logs
        HubEventType.USER_WARN: log_config.modLogsChannelId,
        HubEventType.USER_BAN: log_config.modLogsChannelId,
        HubEventType.USER_UNBAN: log_config.modLogsChannelId,
        HubEventType.USER_MUTE: log_config.modLogsChannelId,
        HubEventType.USER_UNMUTE: log_config.modLogsChannelId,
        # Server moderation events -> mod logs
        HubEventType.SERVER_WARN: log_config.modLogsChannelId,
        HubEventType.SERVER_BAN: log_config.modLogsChannelId,
        HubEventType.SERVER_UNBAN: log_config.modLogsChannelId,
        HubEventType.SERVER_MUTE: log_config.modLogsChannelId,
        HubEventType.SERVER_UNMUTE: log_config.modLogsChannelId,
        # Connection events -> join/leaves
        HubEventType.CONNECTION_ADD: log_config.joinLeavesChannelId,
        HubEventType.CONNECTION_REMOVE: log_config.joinLeavesChannelId,
        # Message events -> message moderation
        HubEventType.MESSAGE_EDIT: log_config.messageModerationChannelId,
        HubEventType.MESSAGE_DELETE: log_config.messageModerationChannelId,
        HubEventType.MESSAGE_REPORT: log_config.reportsChannelId,
        HubEventType.NSFW_DETECTED: log_config.networkAlertsChannelId,
        # Appeals
        HubEventType.APPEAL_SUBMITTED: log_config.appealsChannelId,
    }

    return event_channel_mapping.get(event_type)


# TODO: The embed look ugly fix it @bread
def create_log_embed(emotes: 'EmojiManager', event: HubEvent) -> discord.Embed:
    # Base embed setup
    embed = discord.Embed(timestamp=event.timestamp, color=get_embed_color(event.event_type))

    # Set title and description based on event type
    title, description = get_embed_title_description(emotes, event)
    embed.title = title
    embed.description = description

    # Add hub information
    embed.add_field(name='Hub', value=f'{event.hub_name} (`{event.hub_id}`)', inline=True)

    # Add moderator information if available
    if event.moderator_id and event.moderator_name:
        embed.add_field(
            name='Moderator',
            value=f'{event.moderator_name} (`{event.moderator_id}`)',
            inline=True,
        )

    # Add target information based on event type
    if event.target_user_id and event.target_user_name:
        embed.add_field(
            name='Target User',
            value=f'{event.target_user_name} (`{event.target_user_id}`)',
            inline=True,
        )
    elif event.target_server_id and event.target_server_name:
        embed.add_field(
            name='Target Server',
            value=f'{event.target_server_name} (`{event.target_server_id}`)',
            inline=True,
        )

    # Add reason if provided
    if event.reason:
        embed.add_field(
            name='Reason',
            value=(
                event.reason[:1024] if len(event.reason) > 1024 else event.reason
            ),  # Respect discord limits
            inline=False,
        )

    # Add duration and expiration information
    if event.duration:
        embed.add_field(name='Duration', value=event.duration, inline=True)

    if event.expires_at:
        embed.add_field(
            name='Expires',
            value=f'<t:{int(event.expires_at.timestamp())}:F>',
            inline=True,
        )

    # Add message-specific information
    if event.message_id:
        embed.add_field(name='Message ID', value=event.message_id, inline=True)

    if event.channel_id:
        embed.add_field(name='Channel', value=f'<#{event.channel_id}>', inline=True)

    if event.original_content and event.new_content:
        # For edit events
        embed.add_field(
            name='Original Content',
            value=(
                event.original_content[:1024]
                if len(event.original_content) > 1024
                else event.original_content
            ),
            inline=False,
        )
        embed.add_field(
            name='New Content',
            value=(
                event.new_content[:1024] if len(event.new_content) > 1024 else event.new_content
            ),
            inline=False,
        )
    elif event.original_content:
        # For delete events
        embed.add_field(
            name='Deleted Content',
            value=(
                event.original_content[:1024]
                if len(event.original_content) > 1024
                else event.original_content
            ),
            inline=False,
        )

    # Add footer with event type
    embed.set_footer(text=f'Event: {event.event_type.value}')

    return embed


def get_embed_color(event_type: HubEventType) -> discord.Color:
    """Get the appropriate embed color for an event type."""
    color_mapping = {
        # Hub management - blue tones
        HubEventType.HUB_DELETE: discord.Color.red(),
        HubEventType.HUB_CREATE: discord.Color.green(),
        HubEventType.HUB_UPDATE: discord.Color.blue(),
        # User moderation - warning/punishment colors
        HubEventType.USER_WARN: discord.Color.orange(),
        HubEventType.USER_BAN: discord.Color.red(),
        HubEventType.USER_UNBAN: discord.Color.green(),
        HubEventType.USER_MUTE: discord.Color.orange(),
        HubEventType.USER_UNMUTE: discord.Color.green(),
        # Server moderation - similar to user but distinct
        HubEventType.SERVER_WARN: discord.Color.gold(),
        HubEventType.SERVER_BAN: discord.Color.dark_red(),
        HubEventType.SERVER_UNBAN: discord.Color.dark_green(),
        HubEventType.SERVER_MUTE: discord.Color.gold(),
        HubEventType.SERVER_UNMUTE: discord.Color.dark_green(),
        # Connection events - neutral colors
        HubEventType.CONNECTION_ADD: discord.Color.green(),
        HubEventType.CONNECTION_REMOVE: discord.Color.yellow(),
        # Message events - purple/magenta tones
        HubEventType.MESSAGE_EDIT: discord.Color.blurple(),
        HubEventType.MESSAGE_DELETE: discord.Color.purple(),
        HubEventType.MESSAGE_REPORT: discord.Color.magenta(),
    }

    return color_mapping.get(event_type, discord.Color.blue())


def get_embed_title_description(emotes: 'EmojiManager', event: HubEvent) -> tuple[str, str]:
    """Get the title and description for an event embed."""
    emoji_mapping = {
        # Hub management
        HubEventType.HUB_DELETE: emotes.delete,
        HubEventType.HUB_CREATE: emotes.plus_icon,
        HubEventType.HUB_UPDATE: emotes.edit_icon,
        # User moderation
        HubEventType.USER_WARN: emotes.alert_icon,
        HubEventType.USER_BAN: emotes.hammer_icon,
        HubEventType.USER_UNBAN: emotes.tick,
        HubEventType.USER_MUTE: '🔇',
        HubEventType.USER_UNMUTE: '🔊',
        # Server moderation
        HubEventType.SERVER_WARN: emotes.alert_icon,
        HubEventType.SERVER_BAN: emotes.hammer_icon,
        HubEventType.SERVER_UNBAN: emotes.tick,
        HubEventType.SERVER_MUTE: '🔇',
        HubEventType.SERVER_UNMUTE: '🔊',
        # Connection events
        HubEventType.CONNECTION_ADD: '🔗',
        HubEventType.CONNECTION_REMOVE: '🔓',
        # Message events
        HubEventType.MESSAGE_EDIT: emotes.edit_icon,
        HubEventType.MESSAGE_DELETE: emotes.delete_icon,
        HubEventType.MESSAGE_REPORT: emotes.report_icon,
    }

    title_mapping = {
        # Hub management
        HubEventType.HUB_DELETE: 'Hub Deleted',
        HubEventType.HUB_CREATE: 'Hub Created',
        HubEventType.HUB_UPDATE: 'Hub Updated',
        # User moderation
        HubEventType.USER_WARN: 'User Warned',
        HubEventType.USER_BAN: 'User Banned',
        HubEventType.USER_UNBAN: 'User Unbanned',
        HubEventType.USER_MUTE: 'User Muted',
        HubEventType.USER_UNMUTE: 'User Unmuted',
        # Server moderation
        HubEventType.SERVER_WARN: 'Server Warned',
        HubEventType.SERVER_BAN: 'Server Banned',
        HubEventType.SERVER_UNBAN: 'Server Unbanned',
        HubEventType.SERVER_MUTE: 'Server Muted',
        HubEventType.SERVER_UNMUTE: 'Server Unmuted',
        # Connection events
        HubEventType.CONNECTION_ADD: 'Server Connected',
        HubEventType.CONNECTION_REMOVE: 'Server Disconnected',
        # Message events
        HubEventType.MESSAGE_EDIT: 'Message Edited',
        HubEventType.MESSAGE_DELETE: 'Message Deleted',
        HubEventType.MESSAGE_REPORT: 'Message Reported',
    }

    emoji = emoji_mapping.get(event.event_type, '📋')
    title = title_mapping.get(event.event_type, 'Hub Event')

    description = get_event_description(event.event_type)

    return f'{emoji} {title}', description


def get_event_description(event_type: HubEventType) -> str:
    """Get a descriptive text for each event type."""
    descriptions = {
        # Hub management
        HubEventType.HUB_DELETE: 'A hub was permanently deleted.',
        HubEventType.HUB_CREATE: 'A new hub was created.',
        HubEventType.HUB_UPDATE: "A hub's settings were modified.",
        # User moderation
        HubEventType.USER_WARN: 'A user received a warning in the hub.',
        HubEventType.USER_BAN: 'A user was banned from the hub. Users can appeal this decision using /appeal.',
        HubEventType.USER_UNBAN: "A user's ban was revoked.",
        HubEventType.USER_MUTE: 'A user was muted in the hub. Users can appeal this decision using /appeal.',
        HubEventType.USER_UNMUTE: "A user's mute was revoked.",
        # Server moderation
        HubEventType.SERVER_WARN: 'A server received a warning in the hub.',
        HubEventType.SERVER_BAN: 'A server was banned from the hub. Server owners can appeal this decision using /appeal.',
        HubEventType.SERVER_UNBAN: "A server's ban was revoked.",
        HubEventType.SERVER_MUTE: 'A server was muted in the hub. Server owners can appeal this decision using /appeal.',
        HubEventType.SERVER_UNMUTE: "A server's mute was revoked.",
        # Connection events
        HubEventType.CONNECTION_ADD: 'A server connected to the hub.',
        HubEventType.CONNECTION_REMOVE: 'A server disconnected from the hub.',
        # Message events
        HubEventType.MESSAGE_EDIT: 'A message was edited in the hub.',
        HubEventType.MESSAGE_DELETE: 'A message was deleted in the hub.',
        HubEventType.MESSAGE_REPORT: 'A message was reported in the hub.',
    }

    return descriptions.get(event_type, 'A hub-related action occurred.')


# New lightweight helpers for event-specific custom logging
async def send_custom_log_to_hub(
    bot: 'Bot',
    hub_id: str,
    event_type: HubEventType,
    embed: discord.Embed,
    view: Optional[discord.ui.View] = None,
) -> bool:
    """Send a custom embed (and optional view) to the hub-configured log channel for the given event type.

    Returns True if a message was sent, False otherwise.
    """
    log_config = await get_hub_log_config(bot.db, hub_id)
    if not log_config:
        logger.debug(f'No log configuration found for hub {hub_id}')
        return False

    channel_str = get_log_channel_for_event(event_type, log_config)
    if not channel_str:
        logger.debug(f'No log channel configured for event type {event_type.value}')
        return False

    try:
        channel_id = int(channel_str)
    except Exception:
        logger.warning(f'Invalid channel id configured for hub {hub_id}: {channel_str}')
        return False

    channel = bot.get_channel(channel_id) or await bot.fetch_channel(channel_id)
    if not isinstance(
        channel,
        (
            discord.TextChannel,
            discord.VoiceChannel,
            discord.StageChannel,
            discord.Thread,
            discord.DMChannel,
        ),
    ):
        logger.warning(f'Log channel {channel_id} is not messageable or not found')
        return False

    # Permission check for guild text channels
    if hasattr(channel, 'guild') and channel.guild:
        bot_member = channel.guild.me
        if bot_member:
            perms = channel.permissions_for(bot_member)
            if not perms.send_messages or not perms.embed_links:
                logger.warning(f'Bot lacks permission to send embeds in log channel {channel_id}')
                return False

    if view is not None:
        await channel.send(embed=embed, view=view)
    else:
        await channel.send(embed=embed)
    return True


async def send_staff_log(
    bot: 'Bot', embed: discord.Embed, view: Optional[discord.ui.View] = None
) -> bool:
    """
    Send a custom embed (and optional view) to the global InterChat staff reports channel.

    Returns:
        True if a message was sent, False otherwise.
    """
    channel_id = bot.constants.staff_report_channel_id()
    if not channel_id:
        logger.warning('STAFF_REPORT_CHANNEL_ID not configured; cannot deliver report log to staff')
        return False

    channel = bot.get_channel(channel_id) or await bot.fetch_channel(channel_id)
    if not isinstance(
        channel,
        (
            discord.TextChannel,
            discord.VoiceChannel,
            discord.StageChannel,
            discord.Thread,
            discord.DMChannel,
        ),
    ):
        logger.warning(f'Staff reports channel {channel_id} is not messageable or not found')
        return False

    if view is not None:
        await channel.send(embed=embed, view=view)
    else:
        await channel.send(embed=embed)

    return True
