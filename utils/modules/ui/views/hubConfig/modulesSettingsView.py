from typing import TYPE_CHECKING, Any, Optional

import discord

from utils.constants import logger
from utils.modules.core.i18n import t
from utils.modules.core.db.models import Hub
from utils.modules.core.hub.SettingsBitField import HubSettingsBitField
from utils.modules.hub.constants import HubPer<PERSON>Level
from utils.modules.ui.views.hubConfig.utils import (
    BaseHubView,
    DatabaseService,
    EmbedFactory,
    Messages,
)
from utils.utils import get_hub

if TYPE_CHECKING:
    from main import Bot
    from utils.modules.ui.views.hubConfig.hubConfigurationView import ConfigurationView


class ModulesSettingsView(BaseHubView):
    """View for managing hub module settings"""

    MODULE_DEFINITIONS = {
        'Reactions': {
            'description': t('ui.hubConfig.modules.reactions.description', locale='en'),
            'emoji': None,  # Will be set from bot.emotes.person_icon
        },
        'HideLinks': {
            'description': t('ui.hubConfig.modules.hideLinks.description', locale='en'),
            'emoji': None,  # Will be set from bot.emotes.link_icon
        },
        'SpamFilter': {
            'description': t('ui.hubConfig.modules.spamFilter.description', locale='en'),
            'emoji': None,  # Will be set from bot.emotes.rules_icon
        },
        'BlockInvites': {
            'description': t('ui.hubConfig.modules.blockInvites.description', locale='en'),
            'emoji': None,  # Will be set from bot.emotes.hash_icon
        },
        'UseNicknames': {
            'description': t('ui.hubConfig.modules.useNicknames.description', locale='en'),
            'emoji': None,  # Will be set from bot.emotes.lightbulb_icon
        },
        'BlockNSFW': {
            'description': 'Block NSFW content even in NSFW hubs',
            'emoji': None,  # Will be set from bot.emotes.alert_icon
        },
        'AllowVideos': {
            'description': 'Allow video attachments in messages',
            'emoji': None,  # Will be set from bot.emotes.construction_icon
        },
    }

    def __init__(
        self,
        bot: 'Bot',
        user: discord.User | discord.Member,
        hub: Hub,
        constants: Any,
        permission: HubPermissionLevel,
        parent_view: Optional['ConfigurationView'] = None,
    ):
        super().__init__(bot, user, hub, constants, permission)
        self.parent_view = parent_view
        self.current_settings: Optional[HubSettingsBitField] = None

        self._setup_modules_select()
        self.add_back_button(self.parent_view)
        self.add_dashboard_button()

    def _setup_modules_select(self):
        """Setup the modules select menu"""
        options = []

        emoji_map = {
            'Reactions': self.bot.emotes.person_icon,
            'HideLinks': self.bot.emotes.link_icon,
            'SpamFilter': self.bot.emotes.rules_icon,
            'BlockInvites': self.bot.emotes.hash_icon,
            'UseNicknames': self.bot.emotes.lightbulb_icon,
            'BlockNSFW': self.bot.emotes.alert_icon,
            'AllowVideos': self.bot.emotes.construction_icon,
        }

        for module_name, config in self.MODULE_DEFINITIONS.items():
            options.append(
                discord.SelectOption(
                    label=module_name,
                    description=config['description'],
                    value=module_name,
                    emoji=emoji_map.get(module_name),
                )
            )

        self.modules_select_menu = discord.ui.Select(
            placeholder='Select modules to toggle...',
            options=options,
            min_values=1,
            max_values=len(options),
            row=0,
        )

        self.modules_select_menu.callback = self._modules_select_callback
        self.add_item(self.modules_select_menu)

    async def _modules_select_callback(self, interaction: discord.Interaction['Bot']):
        """Handle module selection"""
        if not await self.interaction_check(interaction):
            return

        await interaction.response.defer()

        # Enable apply button
        for item in self.children:
            if isinstance(item, discord.ui.Button) and item.label == 'Apply Changes':
                item.disabled = False
                break

        # Update select menu to show selections
        selected_values = set(self.modules_select_menu.values)
        for option in self.modules_select_menu.options:
            option.default = option.value in selected_values

        await interaction.edit_original_response(view=self)

    async def create_modules_embed(self) -> discord.Embed:
        """Create the modules settings embed"""
        async with self.bot.db.get_session() as session:
            db_hub = await get_hub(self.hub.id, session)
            if not db_hub:
                return EmbedFactory.error(self.bot, 'Error', Messages.HUB_NOT_FOUND)

        settings_value = getattr(db_hub, 'settings', 0) or 0
        self.current_settings = HubSettingsBitField(settings_value)

        embed = discord.Embed(color=self.constants.color())
        embed.description = (
            f'### {self.bot.emotes.gear_icon} Hub Modules\n'
            'Configure additional features and moderation tools for your hub*'
        )

        # Create status display
        modules_status = []
        for flag_name in self.MODULE_DEFINITIONS:
            is_enabled = self.current_settings.has(flag_name)
            status_icon = self.bot.emotes.tick_icon if is_enabled else self.bot.emotes.x_icon
            status_text = '**Enabled**' if is_enabled else 'Disabled'
            description = self.MODULE_DEFINITIONS[flag_name]['description']

            modules_status.append(f'{status_icon} **{flag_name}**: {status_text}\n-# {description}')

        # Split into columns
        mid_point = len(modules_status) // 2
        if modules_status:
            embed.add_field(
                name='\u200b',
                value='\n\n'.join(modules_status[:mid_point]),
                inline=True,
            )
            if len(modules_status) > mid_point:
                embed.add_field(
                    name='\u200b',
                    value='\n\n'.join(modules_status[mid_point:]),
                    inline=True,
                )

        # Footer
        last_updated = (
            db_hub.updatedAt.strftime('%B %d, %Y at %I:%M %p') if db_hub.updatedAt else 'Never'
        )
        embed.set_footer(
            text=f'Hub: {db_hub.name} • Last Updated: {last_updated}',
            icon_url=db_hub.iconUrl
            or (self.bot.user.display_avatar.url if self.bot.user else None),
        )

        return embed

    @discord.ui.button(
        label='Apply Changes',
        style=discord.ButtonStyle.green,
        disabled=True,
        row=1,
    )
    async def apply_changes_button(
        self, interaction: discord.Interaction['Bot'], button: discord.ui.Button
    ):
        """Apply module changes"""
        if not await self.interaction_check(interaction):
            return

        await interaction.response.defer()

        if not self.modules_select_menu or not self.modules_select_menu.values:
            await interaction.followup.send('No modules selected.', ephemeral=True)
            return

        try:
            # Ensure we have current settings
            if self.current_settings is None:
                async with self.bot.db.get_session() as session:
                    db_hub = await get_hub(self.hub.id, session)
                    if not db_hub:
                        await interaction.followup.send(Messages.HUB_NOT_FOUND, ephemeral=True)
                        return
                    settings_value = getattr(db_hub, 'settings', 0) or 0
                self.current_settings = HubSettingsBitField(settings_value)

            # Toggle selected flags
            changes_made = []
            for flag_name in self.modules_select_menu.values:
                if flag_name in HubSettingsBitField.FLAGS:
                    was_enabled = self.current_settings.has(flag_name)

                    if was_enabled:
                        self.current_settings.remove(flag_name)
                        changes_made.append(f'{self.bot.emotes.x_icon} Disabled **{flag_name}**')
                    else:
                        self.current_settings.add(flag_name)
                        changes_made.append(f'{self.bot.emotes.tick_icon} Enabled **{flag_name}**')

            # Update database
            success = await DatabaseService.update_hub(
                self.bot, self.hub.id, settings=self.current_settings.get()
            )

            if not success:
                await interaction.followup.send(Messages.HUB_UPDATE_FAILED, ephemeral=True)
                return

            # Refresh UI
            updated_embed = await self.create_modules_embed()

            # Reset apply button and selections
            button.disabled = True
            for option in self.modules_select_menu.options:
                option.default = False

            await interaction.edit_original_response(embed=updated_embed, view=self)

            if changes_made:
                success_embed = EmbedFactory.success(
                    self.bot,
                    'Modules Updated!',
                    f'Successfully updated {len(changes_made)} module setting(s):\n\n'
                    + '\n'.join(changes_made),
                )
                await interaction.followup.send(embed=success_embed, ephemeral=True)

        except Exception as e:
            logger.error(f'Module update error: {e}')
            await interaction.followup.send(f'An error occurred: {str(e)}', ephemeral=True)
