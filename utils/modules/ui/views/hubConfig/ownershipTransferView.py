from typing import TYPE_CHECKING
import discord
from sqlalchemy import select

from utils.modules.core.checks import interaction_check
from utils.modules.core.db.models import Hub
from utils.modules.ui.views.hubConfig.utils import BaseHubView

if TYPE_CHECKING:
    from main import Bot
    from utils.modules.ui.views.hubConfig.hubConfigurationView import ConfigurationView


class OwnershipTransfer(BaseHubView):
    def __init__(
        self,
        bot,
        user: discord.User | discord.Member,
        hub: Hub,
        parent_view: 'ConfigurationView',
    ):
        super().__init__(bot, user, hub, parent_view.permission, timeout=90)
        self.bot = bot
        self.user = user
        self.hub = hub
        self.constants = bot.constants
        self.parent_view = parent_view
        self.selected_user: discord.User | discord.Member

        # Add back button if parent view is provided
        if self.parent_view:
            back_button = discord.ui.Button(
                label='Back',
                style=discord.ButtonStyle.secondary,
                row=1,
            )
            back_button.callback = self._back_callback
            self.add_item(back_button)

    async def _back_callback(self, interaction: discord.Interaction['Bot']):
        """Handle the back button callback to return to main configuration menu."""
        if not await self.interaction_check(interaction):
            return

        if self.parent_view:
            embed = discord.Embed(
                color=self.constants.color(),
                title=f'{self.bot.emotes.gear_icon} Hub Configuration',
                description="Customize your InterChat hub for your community. Pick what you'd like to configure below.",
            )

            await interaction.response.edit_message(embed=embed, view=self.parent_view)
        else:
            await interaction.response.send_message(
                f'{self.bot.emotes.x_icon} Unable to return to main menu.', ephemeral=True
            )

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """Verify the user can interact with this view."""
        if interaction.user.id != self.user.id:
            await interaction.response.send_message("You can't use this interface.", ephemeral=True)
            return False
        return True

    @discord.ui.select(
        placeholder='Select a user', max_values=1, min_values=1, cls=discord.ui.UserSelect
    )
    async def on_submit(
        self, interaction: discord.Interaction['Bot'], select: discord.ui.UserSelect
    ):
        await interaction.response.defer()
        await interaction_check(interaction, self.user, interaction.user)

        self.selected_user = select.values[0]
        self.confirm_callback.disabled = False

        await interaction.edit_original_response(view=self)

    @discord.ui.button(label='Confirm', style=discord.ButtonStyle.red, disabled=True)
    async def confirm_callback(
        self, interaction: discord.Interaction['Bot'], button: discord.ui.Button
    ):
        await interaction.response.defer()
        await interaction_check(interaction, self.user, interaction.user)

        embed = discord.Embed(
            title='Warning',
            description=f'Are you sure you would like to **permenantly** transfer hub ownership to {self.selected_user.mention}?',
            color=discord.Color.red(),
        )
        view = TransferConfirmationView(
            self.bot, self.user, self.hub, self.selected_user, parent_view=self
        )

        await interaction.edit_original_response(embed=embed, view=view)


class TransferConfirmationView(BaseHubView):
    def __init__(
        self,
        bot,
        user: discord.User | discord.Member,
        hub: Hub,
        selected: discord.User | discord.Member,
        parent_view: 'OwnershipTransfer',
    ):
        super().__init__(bot, user, hub, parent_view.permission, timeout=20)
        self.bot = bot
        self.user = user
        self.hub = hub
        self.constants = bot.constants
        self.selected_user = selected
        self.parent_view = parent_view

    @discord.ui.button(label='Confirm', style=discord.ButtonStyle.red)
    async def confirm_callback(
        self, interaction: discord.Interaction['Bot'], button: discord.ui.Button
    ):
        await interaction.response.defer()
        await interaction_check(interaction, self.user, interaction.user)

        async with self.bot.db.get_session() as session:
            stmt = select(Hub).where(Hub.id == self.hub.id)
            result = (await session.execute(stmt)).scalar()

            result.ownerId = str(self.selected_user.id)
            await session.commit()

        embed = discord.Embed(
            title='Success!',
            description=f'Ownership of **{self.hub.name}** has succesfully been transferred to {self.selected_user.mention}.',
            color=self.constants.color(),
        )
        view = None
        await interaction.edit_original_response(embed=embed, view=view)

    @discord.ui.button(label='Cancel', style=discord.ButtonStyle.grey)
    async def cancel_callback(
        self, interaction: discord.Interaction['Bot'], button: discord.ui.Button
    ):
        await interaction.response.defer()
        await interaction_check(interaction, self.user, interaction.user)

        embed = discord.Embed(
            title='Cancelled',
            description='Operation cancelled. I have not transferred hub ownership.',
            color=self.constants.color(),
        )
        view = None
        await interaction.edit_original_response(embed=embed, view=view)
