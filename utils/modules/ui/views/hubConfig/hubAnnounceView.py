from typing import TYPE_CHECKING, Optional

import discord
from discord.ui import <PERSON><PERSON>, Select, View, button
from sqlalchemy import select

from utils.constants import logger
from utils.modules.core.checks import interaction_check
from utils.modules.core.db.models import Hub, HubAnnouncement
from utils.modules.ui.views.hubAnnounceViews import CreateModal
from utils.utils import ms_to_human

if TYPE_CHECKING:
    from main import Bot


class ConfigureAnnoucement(View):
    def __init__(self, bot, user, hub, constants, announcement):
        super().__init__(timeout=300)
        self.bot: 'Bot' = bot
        self.user: discord.User | discord.Member = user
        self.hub: Hub = hub
        self.constants = constants
        self.announcement: HubAnnouncement = announcement

        self.action_select = Select(
            placeholder='Select an action',
            options=[
                discord.SelectOption(
                    emoji=self.bot.emotes.gear_icon,
                    label='Edit',
                    description='Edit the selected announcement.',
                    value='mod',
                ),
                discord.SelectOption(
                    emoji=self.bot.emotes.delete_icon,
                    label='Remove',
                    description='Remove the selected announcement',
                    value='rm',
                ),
            ],
            min_values=1,
            max_values=1,
        )
        self.action_select.callback = self.action_select_callback
        self.add_item(self.action_select)

    async def action_select_callback(self, interaction: discord.Interaction['Bot']):
        await interaction_check(interaction, self.user, interaction.user)

        if self.action_select.values[0] == 'rm':
            await interaction.response.defer()
            async with self.bot.db.get_session() as session:
                await session.delete(self.announcement)
                await session.commit()

            embed = discord.Embed(
                title='Success!',
                description=f'{self.bot.emotes.tick} Your announcement has been **deleted**. It will no longer be sent automatically.',
                color=discord.Color.green(),
            )
            await interaction.edit_original_response(embed=embed, view=None)

        elif self.action_select.values[0] == 'mod':
            modal = CreateModal(
                self.bot, self.user, self.constants, self.hub, True, None, self.announcement
            )
            await interaction.response.send_modal(modal)


class HubAnnouncements(View):
    def __init__(self, bot, user, hub, constants, parent_view):
        super().__init__(timeout=300)
        self.bot: 'Bot' = bot
        self.user: discord.User | discord.Member = user
        self.hub: Hub = hub
        self.constants = constants
        self.parent_view = parent_view

        self.announcement_select = Select(
            placeholder='Select an announcement',
            options=[discord.SelectOption(label='Loading...', value='NONE')],
            disabled=True,
            min_values=1,
            max_values=1,
        )
        self.announcement_select.callback = self.announcement_select_callback
        self.add_item(self.announcement_select)

        self.load_button()

    def load_button(self):
        self.schedule_button.emoji = self.bot.emotes.megaphone_icon
        self.schedule_button.disabled = False

    async def load_select(self, interaction: Optional[discord.Interaction] = None):
        async with self.bot.db.get_session() as session:
            stmt = select(HubAnnouncement).where(HubAnnouncement.hubId == self.hub.id)
            result = (await session.execute(stmt)).scalars().all()

        if result:
            self.announcement_select.options.clear()
            for option in result:
                self.announcement_select.add_option(
                    label=option.title,
                    description=f'Occurs every: {ms_to_human(option.frequencyMs)}',
                    value=str(option.id),
                )
            self.announcement_select.disabled = False

        if interaction:
            await interaction.edit_original_response(view=self)

    async def announcement_select_callback(self, interaction: discord.Interaction['Bot']):
        await interaction.response.defer()
        await interaction_check(interaction, self.user, interaction.user)

        async with self.bot.db.get_session() as session:
            stmt = select(HubAnnouncement).where(
                HubAnnouncement.id == self.announcement_select.values[0]
            )
            result = await session.scalar(stmt)

        if not result:
            await interaction.edit_original_response(content='Announcement not found.', view=None)
            return

        embed = discord.Embed(
            title='Announcement Configuration',
            description='Information regarding this announcement will be shown below. You may modify or remove it using the select.',
            color=self.constants.color(),
        )
        embed.add_field(
            name=result.title,
            value=f'> **Next Occurrence:** <t:{int(result.nextAnnouncement.timestamp())}:R>\n> **Frequency:** {result.frequencyMs}',
            inline=True,
        )
        embed.add_field(name='Content', value=result.content, inline=False)
        view = ConfigureAnnoucement(self.bot, self.user, self.hub, self.constants, result)
        await interaction.edit_original_response(embed=embed, view=view)

    @button(label='Schedule', style=discord.ButtonStyle.grey, row=1, disabled=True)
    async def schedule_button(self, interaction: discord.Interaction['Bot'], button: Button):
        try:
            await interaction_check(interaction, self.user, interaction.user)

            modal = CreateModal(self.bot, self.user, self.constants, self.hub, True, self, None)
            await interaction.response.send_modal(modal)
        except Exception as e:
            logger.error(e)
