from datetime import datetime
from typing import TYPE_CHECKING, Callable, Optional

import discord
from sqlalchemy import select
from sqlalchemy.orm import selectinload

from utils.constants import logger
from utils.modules.core.db.models import Hub, HubModerator
from utils.modules.core.i18n import t
from utils.modules.hub.constants import Hub<PERSON><PERSON><PERSON>Level
from utils.modules.ui.CustomModal import CustomModal
from utils.modules.ui.views.BaseView import BaseView
from utils.utils import get_hub

if TYPE_CHECKING:
    from main import Bot
    from utils.modules.core.db.database import Database

# ===================== CONSTANTS =====================


class Messages:
    ERROR_TITLE = t('ui.common.titles.error', locale='en')
    NOT_IMPLEMENTED = t('ui.common.messages.notImplemented', locale='en')
    HUB_NOT_FOUND = t('ui.common.messages.hubNotFound', locale='en')
    INTERFACE_PERMISSION = t('ui.common.messages.interfacePermission', locale='en')
    HUB_UPDATE_FAILED = t('ui.common.messages.hubUpdateFailed', locale='en')
    HUB_CONFIG_DESCRIPTION = t('ui.common.messages.hubConfigDescription', locale='en')


# ===================== UTILITY FUNCTIONS =====================


class DatabaseService:
    """Service class for database operations"""

    @staticmethod
    async def update_hub(bot: 'Bot', hub_id: str, **updates) -> bool:
        """Update hub data in database."""
        try:
            async with bot.db.get_session() as session:
                stmt = select(Hub).where(Hub.id == hub_id)
                hub = (await session.execute(stmt)).scalar()

                if not hub:
                    return False

                for field, value in updates.items():
                    if hasattr(hub, field):
                        setattr(hub, field, value)

                hub.updatedAt = datetime.now()
                await session.commit()
                return True
        except Exception as e:
            logger.error(f'Failed to update hub {hub_id}: {e}')
            return False

    @staticmethod
    async def get_hub_with_moderators(hub_id: str, db: 'Database') -> Optional[Hub]:
        """Get hub with moderators loaded."""
        async with db.get_session() as session:
            stmt = (
                select(Hub)
                .where(Hub.id == hub_id)
                .options(selectinload(Hub.moderators).selectinload(HubModerator.user))
            )
            return (await session.execute(stmt)).scalar()


class EmbedFactory:
    """Factory for creating standardized embeds"""

    @staticmethod
    def success(bot: 'Bot', title: str, description: str, **fields) -> discord.Embed:
        """Create a standardized success embed."""
        embed = discord.Embed(
            title=f'{bot.emotes.tick_icon} {title}',
            description=description,
            color=discord.Color.green(),
        )
        for field_name, field_value in fields.items():
            embed.add_field(name=field_name, value=field_value, inline=False)
        return embed

    @staticmethod
    def error(bot: 'Bot', title: str, description: str) -> discord.Embed:
        """Create a standardized error embed."""
        return discord.Embed(
            title=f'{bot.emotes.x_icon} {title}',
            description=description,
            color=discord.Color.red(),
        )


class ModalHandler:
    @staticmethod
    async def handle_modal_edit(
        interaction: discord.Interaction['Bot'],
        bot: 'Bot',
        hub_id: str,
        modal_title: str,
        modal_config: list,
        update_callback: Callable,
        success_title: str,
        success_description: str,
        validation_callback: Optional[Callable] = None,
    ):
        """Generic handler for modal-based edits."""
        try:
            # Get current hub state
            async with bot.db.get_session() as session:
                db_hub = await get_hub(hub_id, session)
                if not db_hub:
                    return await interaction.response.send_message(
                        Messages.HUB_NOT_FOUND, ephemeral=True
                    )

            # Create and show modal
            modal = CustomModal(modal_title, modal_config)
            await interaction.response.send_modal(modal)

            if not await modal.wait():
                return

            # Custom validation if provided
            if validation_callback:
                validation_result = await validation_callback(modal, db_hub)
                if validation_result is not True:
                    return await interaction.followup.send(embed=validation_result, ephemeral=True)

            # Apply updates using callback
            success = await update_callback(modal, db_hub)
            if not success:
                return await interaction.followup.send('Failed to update hub.', ephemeral=True)

            # Send success confirmation
            success_embed = EmbedFactory.success(bot, success_title, success_description)
            await interaction.followup.send(embed=success_embed, ephemeral=True)

        except Exception as e:
            logger.error(f'Modal edit error: {e}')
            await interaction.followup.send(f'An error occurred: {str(e)}', ephemeral=True)


async def get_hub_from_db(bot: 'Bot', hub_id: str) -> Optional[Hub]:
    """Get hub data from database - wrapper for DatabaseService.get_hub."""
    async with bot.db.get_session() as session:
        return await get_hub(hub_id, session)


async def update_hub_in_db(bot: 'Bot', hub_id: str, **updates) -> bool:
    """Update hub data in database - wrapper for DatabaseService.update_hub."""
    return await DatabaseService.update_hub(bot, hub_id, **updates)


async def handle_modal_edit(
    interaction: discord.Interaction['Bot'],
    bot: 'Bot',
    hub_id: str,
    modal_title: str,
    modal_config: list,
    update_callback: Callable,
    success_title: str,
    success_description: str,
    validation_callback: Optional[Callable] = None,
):
    return await ModalHandler.handle_modal_edit(
        interaction,
        bot,
        hub_id,
        modal_title,
        modal_config,
        update_callback,
        success_title,
        success_description,
        validation_callback,
    )


def create_success_embed(bot: 'Bot', title: str, description: str, **fields) -> discord.Embed:
    """wrapper for EmbedFactory.success."""
    return EmbedFactory.success(bot, title, description, **fields)


# ===================== BASE CLASSES =====================


class BaseHubView(BaseView):
    """Hub-specific view that extends BaseView."""

    def __init__(
        self,
        bot: 'Bot',
        user: discord.User | discord.Member,
        hub: 'Hub',
        permission: 'HubPermissionLevel',
        timeout: int = 300,
    ):
        super().__init__(bot, user, timeout)
        self.hub = hub
        self.permission = permission

    def add_dashboard_button(self, row: int = 2):
        """Add a standardized dashboard button for hubs."""
        dashboard_button = discord.ui.Button(
            emoji=self.bot.emotes.globe_icon,
            label='Web Dashboard',
            style=discord.ButtonStyle.link,
            url=f'https://www.interchat.tech/dashboard/hubs/{self.hub.id}',
            row=row,
        )
        self.add_item(dashboard_button)

    def add_back_button(
        self,
        parent_view: Optional[discord.ui.View] = None,
        parent_view_factory: Optional[Callable[[], discord.ui.View]] = None,
        embed_title: Optional[str] = None,
        embed_description: str = Messages.HUB_CONFIG_DESCRIPTION,
        row: int = 2,
    ):
        """Override to provide hub-specific back button behavior."""
        if embed_title is None:
            embed_title = f'{self.bot.emotes.gear_icon} Hub Configuration'

        super().add_back_button(
            parent_view, parent_view_factory, embed_title, embed_description, row
        )
