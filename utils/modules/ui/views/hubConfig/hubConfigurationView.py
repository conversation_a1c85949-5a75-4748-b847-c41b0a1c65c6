from typing import TYPE_CHECKING

import discord
from discord.ui import Select, View

from utils.modules.core.checks import interaction_check
from utils.modules.core.db.models import Hub, HubModeratorRole
from utils.modules.core.i18n import t
from utils.modules.ui.views.hubConfig.generalSettingsView import GeneralSettingsView
from utils.modules.ui.views.hubConfig.hubAnnounceView import HubAnnouncements
from utils.modules.ui.views.hubConfig.hubPermissionsView import HubPermissions
from utils.modules.ui.views.hubConfig.hubRulesView import HubRulesView
from utils.modules.ui.views.hubConfig.loggingSettingsView import LoggingSettingsView
from utils.modules.ui.views.hubConfig.modulesSettingsView import ModulesSettingsView
from utils.modules.ui.views.hubConfig.ownershipTransferView import OwnershipTransfer
from utils.modules.ui.views.hubConfig.utils import DatabaseService
from utils.modules.hub.constants import HubPermissionLevel

if TYPE_CHECKING:
    from main import Bot

HUB_NOT_FOUND_MSG = 'Hub not found. Please try again later.'


class ConfigurationView(View):
    """Main hub configuration view with navigation to different settings sections."""

    def __init__(
        self,
        bot: 'Bot',
        user,
        hub: Hub,
        options,
        permission: HubPermissionLevel,
    ):
        super().__init__(timeout=300)
        self.bot = bot
        self.constants = bot.constants
        self.hub = hub
        self.user = user
        self.options = options
        self.permission = permission
        self.message: discord.Message

        self.setup_select()

    def setup_select(self):
        """Setup the main configuration select menu."""
        self.on_submit.options = self.options
        self.on_submit.placeholder = t('ui.hubConfig.main.placeholder', locale='en')

    @discord.ui.select(
        max_values=1,
        min_values=1,
    )
    async def on_submit(self, interaction: discord.Interaction['Bot'], selected_item: Select):
        """Handle main configuration menu selection."""
        await interaction.response.defer()
        await interaction_check(interaction, interaction.user, self.user)

        match selected_item.values[0]:
            case 'hperm':
                await self._handle_hub_permissions(interaction)
            case 'hsannounce':
                await self._handle_schedule(interaction)
            case 'hgeneral':
                await self._handle_general_settings(interaction)
            case 'hrules':
                await self._handle_hub_rules(interaction)
            case 'hmodules':
                await self._handle_modules_settings(interaction)
            case 'hlogging':
                await self._handle_logging_settings(interaction)
            case 'transfer':
                await self._handle_ownership_transfer(interaction)
            case _:
                await interaction.followup.send(
                    t('ui.common.messages.notImplemented', locale='en'), ephemeral=True
                )

    async def _handle_hub_permissions(self, interaction: discord.Interaction['Bot']):
        """Handle hub permissions configuration."""
        db_hub = await DatabaseService.get_hub_with_moderators(self.hub.id, self.bot.db)

        if not db_hub:
            await interaction.followup.send(HUB_NOT_FOUND_MSG, ephemeral=True)
            return

        # Create the embed showing current hub moderators
        embed = discord.Embed(
            description=f'### {self.bot.emotes.person_icon} Hub Team Management\n> *Manage your team and add new members.*2',
            color=self.constants.color(),
        )

        # Add owner field
        try:
            owner = await self.bot.fetch_user(int(db_hub.ownerId))
        except discord.NotFound:
            owner = None

        owner_str = 'Unknown' if owner is None else f'{owner.name} (`{owner.id}`)'

        embed.add_field(
            name=f'{self.bot.emotes.owner} Hub Owner',
            value=f'**{owner_str}**\n-# *The mastermind behind this hub! ✨*',
            inline=False,
        )

        # Add moderators if any exist
        if db_hub.moderators:
            managers = []
            moderators = []

            for mod in db_hub.moderators:
                user_info = f'**{mod.user.name}** (`{mod.userId}`)'

                if mod.role == HubModeratorRole.MANAGER:
                    managers.append(user_info)
                else:
                    moderators.append(user_info)

            if managers:
                embed.add_field(
                    name=f'{self.bot.emotes.gear_icon} Hub Managers ({len(managers)})',
                    value='\n\n'.join(managers),
                    inline=True,
                )

            if moderators:
                embed.add_field(
                    name=f'{self.bot.emotes.hammer_icon} Moderators ({len(moderators)})',
                    value='\n\n'.join(moderators),
                    inline=True,
                )

        embed.set_footer(text=f'Hub: {self.hub.name} - Total Staff: {1 + len(db_hub.moderators)}')

        view = HubPermissions(
            self.bot,
            interaction.user,
            self.hub,
            parent_view=self,
        )
        await interaction.edit_original_response(embed=embed, view=view)

    async def _handle_schedule(self, interaction: discord.Interaction['Bot']):
        view = HubAnnouncements(
            self.bot, interaction.user, self.hub, self.constants, parent_view=self
        )
        await view.load_select()

        embed = discord.Embed(
            title='Announcement Schedule',
            description='Create, edit, or remove scheduled announcements. These are **automatically** sent your hub in predefined intervals.',
            color=self.constants.color(),
        )
        await interaction.edit_original_response(embed=embed, view=view)

    async def _handle_general_settings(self, interaction: discord.Interaction['Bot']):
        """Handle general hub settings configuration."""
        view = GeneralSettingsView(
            self.bot,
            interaction.user,
            self.hub,
            self.permission,
            parent_view=self,
        )

        # Create the initial embed
        embed = await view.create_general_settings_embed()

        await interaction.edit_original_response(embed=embed, view=view)

    async def _handle_hub_rules(self, interaction: discord.Interaction['Bot']):
        """Handle hub rules configuration."""
        view = HubRulesView(
            self.bot,
            interaction.user,
            self.hub,
            self.constants,
            self.permission,
            parent_view=self,
        )

        # Create the initial embed
        embed = await view.create_rules_embed()

        await interaction.edit_original_response(embed=embed, view=view)

    async def _handle_modules_settings(self, interaction: discord.Interaction['Bot']):
        """Handle hub modules settings configuration."""
        view = ModulesSettingsView(
            self.bot,
            interaction.user,
            self.hub,
            self.constants,
            self.permission,
            parent_view=self,
        )

        # Create the initial embed
        embed = await view.create_modules_embed()

        await interaction.edit_original_response(embed=embed, view=view)

    async def _handle_logging_settings(self, interaction: discord.Interaction['Bot']):
        """Handle hub logging settings configuration."""
        view = LoggingSettingsView(
            self.bot,
            interaction.user,
            self.hub,
            self.permission,
            parent_view=self,
        )

        # Create the initial embed
        embed = await view.create_logging_embed()

        await interaction.edit_original_response(embed=embed, view=view)

    async def _handle_ownership_transfer(self, interaction: discord.Interaction['Bot']):
        """Handle ownership transfer configuration."""
        await interaction_check(interaction, self.user, interaction.user)

        embed = discord.Embed(
            description=(
                f'### {self.bot.emotes.alert_icon} Ownership Transfer\n'
                f'**⚠️ CAUTION: This action is permanent and irreversible!**\n\n'
                f'Transferring hub ownership will:\n'
                f'{self.bot.emotes.x_icon} Remove your owner privileges permanently\n'
                f'{self.bot.emotes.wand_icon} Grant full control to the selected user\n'
                f'{self.bot.emotes.lock_icon} Cannot be undone by staff or developers\n\n'
                f'*Please be absolutely certain before proceeding.*'
            ),
            color=discord.Color.red(),
        )
        view = OwnershipTransfer(self.bot, self.user, self.hub, parent_view=self)
        await interaction.edit_original_response(embed=embed, view=view)
