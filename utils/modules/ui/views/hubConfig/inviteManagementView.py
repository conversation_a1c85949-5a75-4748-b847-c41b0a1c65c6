from typing import TYPE_CHECKING

import discord
from discord.ui import <PERSON><PERSON>, TextInput, View, button
from zuid import ZUID

from utils.modules.core.checks import interaction_check
from utils.modules.core.db.models import Hub, HubInvite
from utils.modules.core.i18n import t
from utils.modules.errors.errorHandler import error_handler
from utils.modules.ui.CustomModal import CustomModal
from utils.utils import ms_to_datetime, parse_duration

if TYPE_CHECKING:
    from main import Bot


class InviteView(View):
    def __init__(self, bot: 'Bot', constants, user, hub: Hub):
        super().__init__(timeout=180)
        self.bot = bot
        self.constants = constants
        self.user = user
        self.hub = hub

    def setup_button(self):
        self.create_invite_callback.emoji = self.bot.emotes.link_icon

    @button(emoji='➕', label='ui.hubConfig.invites.buttons.create', style=discord.ButtonStyle.grey)
    async def create_invite_callback(self, interaction: discord.Interaction['Bot'], button: But<PERSON>):
        await interaction_check(interaction, self.user, interaction.user)

        modal = CustomModal(
            t('ui.hubConfig.invites.create.title', locale='en'),
            [
                (
                    'customcode',
                    TextInput(
                        label=t('ui.hubConfig.invites.create.customCode.label', locale='en'),
                        placeholder=t(
                            'ui.hubConfig.invites.create.customCode.placeholder', locale='en'
                        ),
                        required=False,
                        max_length=40,
                    ),
                ),
                (
                    'uses',
                    TextInput(
                        label=t('ui.hubConfig.invites.create.uses.label', locale='en'),
                        placeholder=t('ui.hubConfig.invites.create.uses.placeholder', locale='en'),
                        required=False,
                    ),
                ),
                (
                    'expire',
                    TextInput(
                        label=t('ui.hubConfig.invites.create.expiry.label', locale='en'),
                        placeholder=t(
                            'ui.hubConfig.invites.create.expiry.placeholder', locale='en'
                        ),
                        required=True,
                    ),
                ),
            ],
        )

        await interaction.response.send_modal(modal)
        await modal.wait()

        generator = ZUID(
            prefix=(
                f'{modal.saved_items["customcode"].value}_'
                if modal.saved_items['customcode'].value
                else ''
            ),
            length=10,
        )

        uses_value = modal.saved_items['uses'].value
        uses = 0  # Default value
        if uses_value is not None and uses_value != '':
            try:
                uses = int(uses_value)
            except Exception as e:
                err = await error_handler(interaction, e)
                if err:
                    raise err

        code = generator()

        invite = HubInvite(
            code=code,
            expires=ms_to_datetime(parse_duration(modal.saved_items['expire'].value)),
            maxUses=uses,
            hubId=self.hub.id,
        )

        async with self.bot.db.get_session() as session:
            session.add(invite)
            await session.commit()

        embed = discord.Embed(
            title='Generated!',
            description=f'{self.bot.emotes.tick_icon} `{code}` can now be used {uses} time(s) to join your hub. Share it with your friends!',
            color=discord.Color.green(),
        )
        embed.set_footer(text=f'Hub: {self.hub.name}')
        await interaction.followup.send(embed=embed, ephemeral=True)
