from typing import TYPE_CHECKING, Optional

import discord
from sqlalchemy import select

from utils.constants import logger
from utils.modules.core.db.models import Hub
from utils.modules.core.i18n import t
from utils.modules.hub.constants import HubPermissionLevel
from utils.modules.ui.CustomModal import CustomModal
from utils.modules.ui.views.hubConfig.utils import BaseHubView
from utils.modules.ui.views.hubConfig.hubRulesComponents import (
    RulesEmbedBuilder,
    RulesUIBuilder,
)
from utils.modules.services.hubRulesService import HubRulesService

if TYPE_CHECKING:
    from main import Bot
    from utils.modules.ui.views.hubConfig.hubConfigurationView import ConfigurationView


class RulesPaginatorView(discord.ui.View):
    """Separate pagination view for hub rules, following the project pattern."""

    def __init__(
        self,
        *,
        bot: 'Bot',
        user: discord.User | discord.Member,
        hub: Hub,
        constants,
        parent_view: Optional['HubRulesView'] = None,
        per_page: int = 10,
        timeout: int = 300,
    ):
        super().__init__(timeout=timeout)
        self.bot = bot
        self.user = user
        self.hub = hub
        self.constants = constants
        self.parent_view = parent_view
        self.per_page = per_page
        self.current_page = 0

        # Add pagination controls
        self.prev_button = discord.ui.Button(
            label=t('ui.common.pagination.previous', locale='en') or 'Previous',
            emoji=self.bot.emotes.arrow_left,
            style=discord.ButtonStyle.secondary,
            row=0,
        )
        self.next_button = discord.ui.Button(
            label=t('ui.common.pagination.next', locale='en') or 'Next',
            emoji=self.bot.emotes.arrow_right,
            style=discord.ButtonStyle.secondary,
            row=0,
        )
        self.page_button = discord.ui.Button(
            label=self._get_page_label(),
            style=discord.ButtonStyle.gray,
            disabled=True,
            row=0,
        )

        self.prev_button.callback = self.on_prev
        self.next_button.callback = self.on_next

        self.add_item(self.prev_button)
        self.add_item(self.page_button)
        self.add_item(self.next_button)

        # Add back button
        if parent_view:
            back_button = discord.ui.Button(
                label=t('ui.setup.buttons.back', locale='en') or 'Back',
                emoji=self.bot.emotes.arrow_left,
                style=discord.ButtonStyle.secondary,
                row=1,
            )
            back_button.callback = self._back_callback
            self.add_item(back_button)

        self._update_buttons()

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """Verify the user can interact with this view."""
        if interaction.user.id != self.user.id:
            await interaction.response.send_message(
                t('ui.common.messages.interfacePermission', locale='en'), ephemeral=True
            )
            return False
        return True

    def _get_page_label(self) -> str:
        """Get the current page label."""
        rules_count = len(self.hub.rules) if self.hub.rules else 0
        total_pages = max(1, (rules_count - 1) // self.per_page + 1) if rules_count > 0 else 1
        return t(
            'ui.common.pagination.page',
            locale='en',
            current=self.current_page + 1,
            total=total_pages,
        )

    def _update_buttons(self):
        """Update button states based on current page."""
        rules_count = len(self.hub.rules) if self.hub.rules else 0
        total_pages = max(1, (rules_count - 1) // self.per_page + 1) if rules_count > 0 else 1

        self.prev_button.disabled = self.current_page <= 0
        self.next_button.disabled = self.current_page >= total_pages - 1
        self.page_button.label = self._get_page_label()

    async def on_prev(self, interaction: discord.Interaction['Bot']):
        """Handle previous page button."""
        if not await self.interaction_check(interaction):
            return

        if self.current_page > 0:
            self.current_page -= 1
            await self._refresh_view(interaction)

    async def on_next(self, interaction: discord.Interaction['Bot']):
        """Handle next page button."""
        if not await self.interaction_check(interaction):
            return

        # Get current rules count
        rules_count = len(self.hub.rules) if self.hub.rules else 0
        total_pages = max(1, (rules_count - 1) // self.per_page + 1) if rules_count > 0 else 1

        if self.current_page < total_pages - 1:
            self.current_page += 1
            await self._refresh_view(interaction)

    async def _back_callback(self, interaction: discord.Interaction['Bot']):
        """Handle back button to return to main rules view."""
        if not await self.interaction_check(interaction):
            return

        if self.parent_view:
            # Refresh parent view with updated data
            await self.parent_view._refresh_view(interaction)

    async def _refresh_view(self, interaction: discord.Interaction['Bot']):
        """Refresh the pagination view with updated data."""
        # Get fresh hub data
        async with self.bot.db.get_session() as session:
            stmt = select(Hub).where(Hub.id == self.hub.id)
            db_hub = (await session.execute(stmt)).scalar()

        if not db_hub:
            await interaction.response.send_message(
                embed=RulesEmbedBuilder.create_error_embed(
                    self.bot, t('ui.common.messages.hubNotFound', locale='en')
                ),
                ephemeral=True,
            )
            return

        # Update hub and clamp page
        self.hub = db_hub
        rules_count = len(self.hub.rules) if self.hub.rules else 0
        if rules_count > 0:
            total_pages = (rules_count - 1) // self.per_page + 1
            self.current_page = max(0, min(self.current_page, total_pages - 1))
        else:
            self.current_page = 0

        # Update UI and send response
        self._update_buttons()
        embed = await self._create_paginated_embed()
        await interaction.response.edit_message(embed=embed, view=self)

    async def _create_paginated_embed(self) -> discord.Embed:
        """Create embed for the current page."""
        # Get fresh rules data
        async with self.bot.db.get_session() as session:
            rules_service = HubRulesService(session)
            rules = await rules_service.get_hub_rules(self.hub.id)

        if rules is None:
            return RulesEmbedBuilder.create_error_embed(
                self.bot, t('ui.common.messages.hubNotFound', locale='en')
            )

        return RulesEmbedBuilder.create_rules_display_embed(
            self.bot,
            self.constants,
            rules,
            self.current_page,
            self.per_page,
            HubRulesService.MAX_RULES,
        )


class RuleActionView(discord.ui.View):
    """Separate view for rule action buttons (edit/delete)."""

    def __init__(self, parent_view: 'HubRulesView', rule_index: int, timeout: int = 300):
        super().__init__(timeout=timeout)
        self.parent_view = parent_view
        self.rule_index = rule_index

        # Add action buttons
        buttons = RulesUIBuilder.create_action_buttons(
            parent_view.bot,
            self._edit_rule_callback,
            self._delete_rule_callback,
            self._cancel_callback,
        )
        for button in buttons:
            self.add_item(button)

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """Verify the user can interact with this view."""
        return await self.parent_view.interaction_check(interaction)

    async def _edit_rule_callback(self, interaction: discord.Interaction['Bot']):
        """Handle edit rule action."""
        if not await self.interaction_check(interaction):
            return
        await self.parent_view.edit_rule_modal(interaction, self.rule_index)

    async def _delete_rule_callback(self, interaction: discord.Interaction['Bot']):
        """Handle delete rule action."""
        if not await self.interaction_check(interaction):
            return
        await self.parent_view.delete_rule_confirmation(interaction, self.rule_index)

    async def _cancel_callback(self, interaction: discord.Interaction['Bot']):
        """Handle cancel action."""
        if not await self.interaction_check(interaction):
            return

        embed = discord.Embed(
            title=f'{self.parent_view.bot.emotes.x_icon} {t("ui.hub.rules.errors.actionCancelled", locale="en")}',
            description=t('ui.hub.rules.errors.actionCancelledDescription', locale='en'),
            color=discord.Color.orange(),
        )
        await interaction.response.edit_message(embed=embed, view=None)


class RuleSelect(discord.ui.Select):
    def __init__(self, parent_view: 'HubRulesView', options: list):
        super().__init__(
            placeholder=t('ui.hub.rules.selectRule.placeholder', locale='en'),
            options=options,
            row=1,
        )
        self.parent_view = parent_view

    async def callback(self, interaction: discord.Interaction['Bot']):  # type: ignore[override]
        try:
            if not await self.parent_view.interaction_check(interaction):
                return

            # Get the selected value from the select menu
            selected_index = int(self.values[0])
            self.parent_view.selected_rule_index = selected_index
            await self.parent_view._show_rule_actions(interaction, selected_index)

        except Exception as e:
            logger.error(f'Error in rule selection callback: {e}')
            await self.parent_view._send_error_response(
                interaction, t('ui.hub.rules.errors.selectionError', locale='en')
            )


class HubRulesView(BaseHubView):
    def __init__(
        self,
        bot: 'Bot',
        user: discord.User | discord.Member,
        hub: Hub,
        constants,
        permission: HubPermissionLevel,
        parent_view: Optional['ConfigurationView'] = None,
    ):
        super().__init__(bot, user, hub, constants, permission)
        self.parent_view = parent_view
        self.selected_rule_index: Optional[int] = None

        # Set up UI components
        self._setup_ui_components()

    def _setup_ui_components(self):
        """Set up all UI components with proper organization."""
        self.clear_items()

        # Row 0: Primary action buttons
        self._add_action_buttons()

        # Row 1: Rule selection dropdown (if rules exist)
        if self.hub.rules:
            self._add_rule_selection()

        # Row 2: Pagination button (if needed) and Navigation buttons
        if self.hub.rules and len(self.hub.rules) > 10:  # More than one page worth
            self._add_pagination_view_button()

        # Row 3: Navigation buttons
        self.add_back_button(self.parent_view, row=3)
        self.add_dashboard_button(row=3)

        # Update button states
        self._update_button_states()

    def _add_action_buttons(self):
        """Add primary action buttons."""
        # Add Rule button
        add_button = discord.ui.Button(
            label=t('ui.hub.rules.addRule.label', locale='en'),
            emoji=self.bot.emotes.plus_icon,
            style=discord.ButtonStyle.green,
            row=0,
        )
        add_button.callback = self._add_rule_callback
        self.add_item(add_button)

        # Refresh button
        refresh_button = discord.ui.Button(
            label=t('ui.setup.buttons.refresh', locale='en') or 'Refresh',
            emoji=self.bot.emotes.refresh_icon,
            style=discord.ButtonStyle.secondary,
            row=0,
        )
        refresh_button.callback = self._refresh_callback
        self.add_item(refresh_button)

    def _add_rule_selection(self):
        """Add rule selection dropdown using custom Select class."""
        if not self.hub.rules:
            return

        # Show first page of rules (up to 10)
        end_idx = min(len(self.hub.rules), 10)
        options = RulesUIBuilder.create_rule_selection_options(self.bot, self.hub.rules, 0, end_idx)

        if options:
            rule_select = RuleSelect(self, options)
            self.add_item(rule_select)

    def _add_pagination_view_button(self):
        """Add a button to access the paginated view for many rules."""
        paginated_button = discord.ui.Button(
            label=t('ui.hub.rules.viewAll.label', locale='en') or 'View All Rules',
            emoji=self.bot.emotes.list_icon,
            style=discord.ButtonStyle.secondary,
            row=2,
        )
        paginated_button.callback = self._view_paginated_callback
        self.add_item(paginated_button)

    def _update_button_states(self):
        """Update button states based on current conditions."""
        current_rule_count = len(self.hub.rules) if self.hub.rules else 0
        can_add_rules = current_rule_count < HubRulesService.MAX_RULES
        has_rules = current_rule_count > 0

        for item in self.children:
            if isinstance(item, discord.ui.Button) and hasattr(item, 'callback'):
                # Update Add Rule button
                if item.callback == self._add_rule_callback:
                    item.disabled = not can_add_rules
                    if not can_add_rules:
                        item.label = t(
                            'ui.hub.rules.errors.maxRulesReached',
                            locale='en',
                            maxRules=HubRulesService.MAX_RULES,
                        )
                        item.style = discord.ButtonStyle.secondary
                    else:
                        item.label = t('ui.hub.rules.addRule.label', locale='en') or 'Add Rule'
                        item.style = discord.ButtonStyle.green

            elif isinstance(item, RuleSelect):
                # Update rule selection dropdown
                item.disabled = not has_rules
                if not has_rules:
                    item.placeholder = t('ui.hub.rules.errors.noRulesAvailable', locale='en')
                else:
                    item.placeholder = (
                        t('ui.hub.rules.selectRule.placeholder', locale='en')
                        or 'Select a rule to edit or delete...'
                    )

    async def create_rules_embed(self) -> discord.Embed:
        """Create the rules display embed."""
        # Get fresh hub data
        async with self.bot.db.get_session() as session:
            rules_service = HubRulesService(session)
            rules = await rules_service.get_hub_rules(self.hub.id)

        if rules is None:
            return RulesEmbedBuilder.create_error_embed(
                self.bot, t('ui.common.messages.hubNotFound', locale='en')
            )

        # Show first page (up to 10 rules) in main view
        return RulesEmbedBuilder.create_rules_display_embed(
            self.bot,
            self.constants,
            rules,
            0,  # Always show page 0 in main view
            10,  # Show up to 10 rules
            HubRulesService.MAX_RULES,
        )

    # Event callbacks
    async def _add_rule_callback(self, interaction: discord.Interaction['Bot']):
        """Handle add rule button click."""
        try:
            if not await self.interaction_check(interaction):
                return
            await self.add_rule_modal(interaction)
        except Exception as e:
            logger.error(f'Error in add rule callback: {e}')
            await self._send_error_response(
                interaction, t('ui.hub.rules.errors.processingError', locale='en')
            )

    async def _refresh_callback(self, interaction: discord.Interaction['Bot']):
        """Handle refresh button click."""
        try:
            if not await self.interaction_check(interaction):
                return
            await self._refresh_view(interaction)
        except Exception as e:
            logger.error(f'Error in refresh callback: {e}')
            await self._send_error_response(
                interaction, t('ui.hub.rules.errors.refreshError', locale='en')
            )

    async def _view_paginated_callback(self, interaction: discord.Interaction['Bot']):
        """Handle view paginated rules button click."""
        try:
            if not await self.interaction_check(interaction):
                return

            # Create paginated view
            paginated_view = RulesPaginatorView(
                bot=self.bot,
                user=self.user,
                hub=self.hub,
                constants=self.constants,
                parent_view=self,
                per_page=10,
                timeout=300,
            )

            # Create embed for paginated view
            embed = await paginated_view._create_paginated_embed()
            await interaction.response.edit_message(embed=embed, view=paginated_view)

        except Exception as e:
            logger.error(f'Error in view paginated callback: {e}')
            await self._send_error_response(
                interaction, t('ui.hub.rules.errors.navigationError', locale='en')
            )

    # Main functionality methods
    async def add_rule_modal(self, interaction: discord.Interaction['Bot']):
        """Show modal for adding new rule."""
        # Validate rules count first
        async with self.bot.db.get_session() as session:
            rules_service = HubRulesService(session)
            current_count = await rules_service.get_rules_count(self.hub.id)

        is_valid, error_msg = HubRulesService.validate_rules_count(current_count, locale='en')
        if not is_valid:
            await interaction.response.send_message(
                embed=RulesEmbedBuilder.create_error_embed(self.bot, error_msg), ephemeral=True
            )
            return

        # Create and show modal
        rule_input = discord.ui.TextInput(
            label=t('ui.hub.rules.addRule.modal.title', locale='en'),
            placeholder=t('ui.hub.rules.addRule.modal.placeholder', locale='en'),
            style=discord.TextStyle.paragraph,
            max_length=HubRulesService.MAX_RULE_LENGTH,
            required=True,
        )

        modal = CustomModal(
            title=t('ui.hub.rules.addRule.modal.title', locale='en'),
            options=[('rule_text', rule_input)],
        )

        await interaction.response.send_modal(modal)

        # Wait for modal submission
        if await modal.wait():
            return  # Modal timed out

        # Ensure we have a valid interaction
        if not modal.interaction:
            return

        # Handle the submission
        await self._handle_add_rule_submit(modal, modal.interaction)

    async def _handle_add_rule_submit(self, modal: CustomModal, interaction: discord.Interaction):
        """Handle add rule modal submission."""
        rule_text = modal.saved_items['rule_text'].value.strip()

        async with self.bot.db.get_session() as session:
            rules_service = HubRulesService(session)
            success, error_msg = await rules_service.add_rule(self.hub.id, rule_text)

        if success:
            success_embed = RulesEmbedBuilder.create_success_embed(
                self.bot, t('ui.hub.rules.success.added', locale='en')
            )
            await interaction.followup.send(embed=success_embed, ephemeral=True)
            # Note: Cannot update original view from modal interaction
        else:
            await interaction.followup.send(
                embed=RulesEmbedBuilder.create_error_embed(self.bot, error_msg), ephemeral=True
            )

    async def edit_rule_modal(self, interaction: discord.Interaction['Bot'], rule_index: int):
        """Show modal for editing existing rule."""
        # Get current rule text
        async with self.bot.db.get_session() as session:
            rules_service = HubRulesService(session)
            current_rule_text = await rules_service.get_rule_at_index(self.hub.id, rule_index)

        if current_rule_text is None:
            await interaction.response.send_message(
                embed=RulesEmbedBuilder.create_error_embed(
                    self.bot, t('ui.hub.rules.errors.ruleNotFound', locale='en')
                ),
                ephemeral=True,
            )
            return

        # Create and show modal
        rule_input = discord.ui.TextInput(
            label=t('ui.hub.rules.editRule.modal.title', locale='en'),
            placeholder=t('ui.hub.rules.editRule.modal.placeholder', locale='en'),
            style=discord.TextStyle.paragraph,
            max_length=HubRulesService.MAX_RULE_LENGTH,
            required=True,
            default=current_rule_text,
        )

        modal = CustomModal(
            title=t('ui.hub.rules.editRule.modal.title', locale='en'),
            options=[('rule_text', rule_input)],
        )

        await interaction.response.send_modal(modal)

        # Wait for modal submission
        if await modal.wait():
            return  # Modal timed out

        # Ensure we have a valid interaction
        if not modal.interaction:
            return

        # Handle the submission
        await self._handle_edit_rule_submit(modal, modal.interaction, rule_index)

    async def _handle_edit_rule_submit(
        self, modal: CustomModal, interaction: discord.Interaction, rule_index: int
    ):
        """Handle edit rule modal submission."""
        rule_text = modal.saved_items['rule_text'].value.strip()

        async with self.bot.db.get_session() as session:
            rules_service = HubRulesService(session)
            success, error_msg = await rules_service.update_rule(self.hub.id, rule_index, rule_text)

        if success:
            success_embed = RulesEmbedBuilder.create_success_embed(
                self.bot, t('ui.hub.rules.success.updated', locale='en')
            )
            await interaction.followup.send(embed=success_embed, ephemeral=True)
            # Note: Cannot update original view from modal interaction
        else:
            await interaction.followup.send(
                embed=RulesEmbedBuilder.create_error_embed(self.bot, error_msg), ephemeral=True
            )

    async def delete_rule_confirmation(
        self, interaction: discord.Interaction['Bot'], rule_index: int
    ):
        """Show confirmation dialog for rule deletion."""
        # Get rule text for confirmation
        async with self.bot.db.get_session() as session:
            rules_service = HubRulesService(session)
            rule_text = await rules_service.get_rule_at_index(self.hub.id, rule_index)

        if rule_text is None:
            await interaction.response.send_message(
                embed=RulesEmbedBuilder.create_error_embed(
                    self.bot, t('ui.hub.rules.errors.ruleNotFound', locale='en')
                ),
                ephemeral=True,
            )
            return

        # Create confirmation view
        confirmation_view = discord.ui.View(timeout=300)
        buttons = RulesUIBuilder.create_confirmation_buttons(
            self.bot,
            lambda i: self._handle_delete_rule_confirm(i, rule_index),
            self._handle_delete_rule_cancel,
        )
        for button in buttons:
            confirmation_view.add_item(button)

        # Create confirmation embed and send
        embed = RulesEmbedBuilder.create_delete_confirmation_embed(self.bot, rule_text, rule_index)
        await interaction.response.send_message(embed=embed, view=confirmation_view, ephemeral=True)

    async def _handle_delete_rule_confirm(
        self, interaction: discord.Interaction['Bot'], rule_index: int
    ):
        """Handle confirmed rule deletion."""
        async with self.bot.db.get_session() as session:
            rules_service = HubRulesService(session)
            success, error_msg = await rules_service.delete_rule(self.hub.id, rule_index)

        if success:
            success_embed = RulesEmbedBuilder.create_success_embed(
                self.bot, t('ui.hub.rules.success.deleted', locale='en')
            )
            await interaction.response.send_message(embed=success_embed, ephemeral=True)
            # Note: Cannot update original view from this interaction context
        else:
            await interaction.response.send_message(
                embed=RulesEmbedBuilder.create_error_embed(self.bot, error_msg), ephemeral=True
            )

    async def _handle_delete_rule_cancel(self, interaction: discord.Interaction['Bot']):
        """Handle cancelled rule deletion."""
        embed = discord.Embed(
            title=f'{self.bot.emotes.checkmark_icon} {t("ui.hub.rules.errors.deletionCancelled", locale="en")}',
            description=t('ui.hub.rules.errors.deletionCancelledDescription', locale='en'),
            color=discord.Color.green(),
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)

    # Helper methods
    async def _show_rule_actions(self, interaction: discord.Interaction['Bot'], rule_index: int):
        """Show edit and delete buttons for selected rule."""
        # Get rule text
        async with self.bot.db.get_session() as session:
            rules_service = HubRulesService(session)
            rule_text = await rules_service.get_rule_at_index(self.hub.id, rule_index)

        if rule_text is None:
            await interaction.response.send_message(
                embed=RulesEmbedBuilder.create_error_embed(
                    self.bot, t('ui.hub.rules.errors.ruleNotFound', locale='en')
                ),
                ephemeral=True,
            )
            return

        # Create action view and embed
        action_view = RuleActionView(self, rule_index, timeout=300)
        embed = RulesEmbedBuilder.create_rule_action_embed(
            self.bot, self.constants, rule_text, rule_index
        )

        await interaction.response.send_message(embed=embed, view=action_view, ephemeral=True)

    async def _refresh_view(self, interaction: discord.Interaction['Bot']):
        """Refresh the view with updated data."""
        # Get fresh hub data
        async with self.bot.db.get_session() as session:
            stmt = select(Hub).where(Hub.id == self.hub.id)
            db_hub = (await session.execute(stmt)).scalar()

        if not db_hub:
            await interaction.response.send_message(
                embed=RulesEmbedBuilder.create_error_embed(
                    self.bot, t('ui.common.messages.hubNotFound', locale='en')
                ),
                ephemeral=True,
            )
            return

        # Update local hub object
        self.hub = db_hub

        # Rebuild UI and update view
        self._setup_ui_components()
        updated_embed = await self.create_rules_embed()
        await interaction.response.edit_message(embed=updated_embed, view=self)

    async def _send_error_response(
        self, interaction: discord.Interaction['Bot'], error_message: str
    ):
        """Send standardized error response."""
        embed = RulesEmbedBuilder.create_error_embed(self.bot, error_message)
        if not interaction.response.is_done():
            await interaction.response.send_message(embed=embed, ephemeral=True)
        else:
            await interaction.followup.send(embed=embed, ephemeral=True)
