from datetime import datetime, timed<PERSON>ta
from typing import TYPE_CHECKING, Optional

import discord
from sqlalchemy import select

from utils.modules.core.db.models import Hub
from utils.modules.core.i18n import t
from utils.modules.hub.constants import HubPermissionLevel
from utils.modules.ui.CustomModal import CustomModal
from utils.modules.ui.views.hubConfig.utils import (
    BaseHubView,
    create_success_embed,
    get_hub_from_db,
    handle_modal_edit,
    update_hub_in_db,
)

if TYPE_CHECKING:
    from main import Bot
    from utils.modules.ui.views.hubConfig.hubConfigurationView import ConfigurationView

HUB_NOT_FOUND_MSG = 'Hub not found. Please try again later.'


class GeneralSettingsView(BaseHubView):
    """View for managing general hub settings."""

    def __init__(
        self,
        bot: 'Bot',
        user: discord.User | discord.Member,
        hub: Hub,
        permission: HubPermissionLevel,
        parent_view: Optional['ConfigurationView'] = None,
    ):
        super().__init__(bot, user, hub, permission)
        self.parent_view = parent_view

        # Set up the select menu with custom emojis
        self._setup_select_menu()
        self.add_back_button(self.parent_view)
        self.add_dashboard_button()

    def _setup_select_menu(self):
        """Set up the select menu with proper emojis."""
        # Find the select menu and update its options with custom emojis
        for item in self.children:
            if isinstance(item, discord.ui.Select) and hasattr(item, 'options'):
                for option in item.options:
                    if option.value == 'edit_description':
                        option.emoji = self.bot.emotes.edit_icon
                    elif option.value == 'edit_name':
                        option.emoji = self.bot.emotes.hash_icon
                    elif option.value == 'edit_welcome':
                        option.emoji = self.bot.emotes.wand_icon
                    elif option.value == 'toggle_nsfw':
                        option.emoji = self.bot.emotes.alert_icon
                    elif option.value == 'toggle_private':
                        option.emoji = self.bot.emotes.lock_icon
        # FIXME: dont use hardcoded locale
        self.settings_select.placeholder = t('ui.hubConfig.general.placeholder', locale='en')
        self.settings_select.options = [
            discord.SelectOption(
                label=t('ui.hubConfig.general.editDescription.label', locale='en'),
                description=t('ui.hubConfig.general.editDescription.description', locale='en'),
                value='edit_description',
            ),
            discord.SelectOption(
                label=t('ui.hubConfig.general.editName.label', locale='en'),
                description=t('ui.hubConfig.general.editName.description', locale='en'),
                value='edit_name',
            ),
            discord.SelectOption(
                label=t('ui.hubConfig.general.welcomeMessage.label', locale='en'),
                description=t('ui.hubConfig.general.welcomeMessage.description', locale='en'),
                value='edit_welcome',
            ),
        ]

    def refresh_view_state(self):
        """Refresh the view state to reflect current hub settings."""
        # Update select menu emojis
        self._setup_select_menu()

    async def create_general_settings_embed(self) -> discord.Embed:
        """Create the general settings embed with current hub data."""
        # Get fresh hub data from database
        async with self.bot.db.get_session() as session:
            stmt = select(Hub).where(Hub.id == self.hub.id)
            db_hub = (await session.execute(stmt)).scalar()

        if not db_hub:
            return discord.Embed(
                title=f'{self.bot.emotes.x_icon} Error',
                description=HUB_NOT_FOUND_MSG,
                color=discord.Color.red(),
            )

        embed = discord.Embed(
            color=self.constants.color(),
            description="### General Settings\n> *Customize your hub's appearance and basic settings below.*",
        )

        embed.add_field(
            name=f'{self.bot.emotes.hash_icon} Hub Name',
            value=f'{db_hub.name} ([Change](https://www.interchat.tech/dashboard/hubs/{db_hub.id}))',
            inline=True,
        )

        # Description field with better formatting
        description_preview = db_hub.description or 'No description set'
        if len(description_preview) > 80:
            description_preview = f'{description_preview[:80]}...'

        embed.add_field(
            name=f'{self.bot.emotes.edit_icon} Hub Description',
            value=description_preview,
            inline=True,
        )

        # empty field
        embed.add_field(
            name='\u200b',
            value='\u200b',
            inline=True,
        )

        if db_hub.nsfw:
            nsfw_status = f'{self.bot.emotes.alert_icon} **NSFW Content** - Adults Only'
        else:
            nsfw_status = f'{self.bot.emotes.tick_icon} **Family Friendly** - Safe for All'

        if db_hub.private:
            private_status = f'{self.bot.emotes.lock_icon} **Private Hub** - Invite Only'
        else:
            private_status = f'{self.bot.emotes.globe_icon} **Public Hub** - Open to All'

        embed.add_field(
            name='Content Rating',
            value=nsfw_status,
            inline=True,
        )

        embed.add_field(
            name='Visibility',
            value=private_status,
            inline=True,
        )

        if not db_hub.welcomeMessage:
            welcome_preview = '-# *Greeting shown to members when they join. Set one below.*'
            welcome_status = f'{self.bot.emotes.x_icon} Not configured'
        else:
            if len(db_hub.welcomeMessage) > 40:
                welcome_preview = f'{db_hub.welcomeMessage[:40]}...'
            else:
                welcome_preview = db_hub.welcomeMessage
            welcome_status = f'{self.bot.emotes.tick_icon} Active'

        embed.add_field(
            name=f'{self.bot.emotes.wave_anim} Welcome Message',
            value=f'{welcome_preview}\n{welcome_status}',
            inline=False,
        )

        embed.add_field(
            name=f'{self.bot.emotes.wand_icon} Advanced Configuration',
            value=f'{self.bot.emotes.fire_icon} For advanced settings like **tags**, **language**, **icon**, and **moderation rules**, visit the dashboard:\n',
            inline=False,
        )

        last_updated = (
            db_hub.updatedAt.strftime('%B %d, %Y at %I:%M %p') if db_hub.updatedAt else 'Never'
        )
        embed.set_footer(
            text=f'Hub: {db_hub.name} • Last Updated: {last_updated}',
            icon_url=db_hub.iconUrl
            or (self.bot.user.display_avatar.url if self.bot.user else None),
        )

        # Update local hub object with fresh data
        self.hub = db_hub

        return embed

    @discord.ui.select(
        row=0,
    )
    async def settings_select(
        self, interaction: discord.Interaction['Bot'], select: discord.ui.Select
    ):
        """Handle selection from the settings select menu."""
        if not await self.interaction_check(interaction):
            return

        selected_value = select.values[0]

        if selected_value == 'edit_description':
            await self.edit_description(interaction)
        elif selected_value == 'edit_name':
            await self.edit_name(interaction)
        elif selected_value == 'edit_welcome':
            await self.edit_welcome(interaction)

    @discord.ui.select(
        placeholder='⚙️ Choose a toggle setting...',
        options=[
            discord.SelectOption(
                label='Toggle NSFW',
                description='Toggle NSFW content rating for your hub',
                value='toggle_nsfw',
            ),
            discord.SelectOption(
                label='Toggle Private',
                description='Toggle between private and public hub visibility',
                value='toggle_private',
            ),
        ],
        row=1,
    )
    async def toggle_settings_select(
        self, interaction: discord.Interaction['Bot'], select: discord.ui.Select
    ):
        """Handle selection from the toggle settings select menu."""
        if not await self.interaction_check(interaction):
            return

        selected_value = select.values[0]

        if selected_value == 'toggle_nsfw':
            await self.handle_toggle_nsfw(interaction)
        elif selected_value == 'toggle_private':
            await self.handle_toggle_private(interaction)

    async def edit_description(self, interaction: discord.Interaction['Bot']):
        """Handle description editing with modal."""
        # Get current hub state
        db_hub = await get_hub_from_db(self.bot, self.hub.id)
        if not db_hub:
            return await interaction.response.send_message(HUB_NOT_FOUND_MSG, ephemeral=True)

        # Configure modal
        modal_config = [
            (
                'description',
                discord.ui.TextInput(
                    label='Hub Description',
                    placeholder='Describe what your hub is about...',
                    style=discord.TextStyle.paragraph,
                    max_length=1000,
                    required=True,
                    default=db_hub.description or '',
                ),
            ),
        ]

        async def update_description(modal: CustomModal, _hub: Hub):
            new_description = modal.saved_items['description'].value
            success = await update_hub_in_db(self.bot, self.hub.id, description=new_description)
            if success:
                self.hub.description = new_description
                # Refresh UI
                updated_embed = await self.create_general_settings_embed()
                self.refresh_view_state()
                try:
                    await interaction.edit_original_response(embed=updated_embed, view=self)
                except discord.NotFound:
                    pass
            return success

        await handle_modal_edit(
            interaction,
            self.bot,
            self.hub.id,
            'Edit Hub Description',
            modal_config,
            update_description,
            'Description Updated!',
            'Your hub description has been successfully updated.',
        )

    async def edit_name(self, interaction: discord.Interaction['Bot']):
        """Handle hub name editing with modal and cooldown check."""
        try:
            async with self.bot.db.get_session() as session:
                stmt = select(Hub).where(Hub.id == self.hub.id)
                db_hub = await session.scalar(stmt)

            if not db_hub:
                return await interaction.response.send_message(HUB_NOT_FOUND_MSG, ephemeral=True)

            # Check cooldown if lastNameChange exists
            if hasattr(db_hub, 'lastNameChange') and db_hub.lastNameChange:
                time_since_change = datetime.now() - db_hub.lastNameChange
                if time_since_change < timedelta(days=10):
                    days_remaining = 10 - time_since_change.days
                    embed = discord.Embed(
                        title=f'{self.bot.emotes.x_icon} Name Change on Cooldown',
                        description=f'You can change your hub name again in **{days_remaining} days**.',
                        color=discord.Color.red(),
                    )
                    return await interaction.response.send_message(embed=embed, ephemeral=True)

            modal = CustomModal(
                'Edit Hub Name',
                [
                    (
                        'name',
                        discord.ui.TextInput(
                            label='Hub Name',
                            placeholder='Enter a new name for your hub...',
                            max_length=50,
                            required=True,
                            default=db_hub.name,
                        ),
                    ),
                ],
            )

            await interaction.response.send_modal(modal)
            if not await modal.wait():
                return

            new_name = modal.saved_items['name'].value.strip()

            # Validate name
            if not new_name:
                embed = discord.Embed(
                    title=f'{self.bot.emotes.x_icon} Invalid Name',
                    description='Hub name cannot be empty.',
                    color=discord.Color.red(),
                )
                return await interaction.followup.send(embed=embed, ephemeral=True)

            # Check if name is already taken
            async with self.bot.db.get_session() as session:
                existing_hub = (
                    await session.execute(
                        select(Hub).where((Hub.name == new_name) & (Hub.id != self.hub.id))
                    )
                ).scalar()

                if existing_hub:
                    embed = discord.Embed(
                        title=f'{self.bot.emotes.x_icon} Name Already Taken',
                        description=f'The name **{new_name}** is already used by another hub. Please choose a different name.',
                        color=discord.Color.red(),
                    )
                    return await interaction.followup.send(embed=embed, ephemeral=True)

                # Update the name
                stmt = select(Hub).where(Hub.id == self.hub.id)
                hub_to_update = await session.scalar(stmt)

                if hub_to_update:
                    old_name = hub_to_update.name
                    hub_to_update.name = new_name

                    # Set cooldown if field exists
                    if hasattr(hub_to_update, 'lastNameChange'):
                        hub_to_update.lastNameChange = datetime.now()

                    await session.commit()

                    # Update local hub object
                    self.hub.name = new_name

                    # Refresh the embed with updated data
                    updated_embed = await self.create_general_settings_embed()
                    self.refresh_view_state()

                    # Update the original message with new embed and view
                    try:
                        await interaction.edit_original_response(embed=updated_embed, view=self)
                    except discord.NotFound:
                        # If the original message is not found, send a new one
                        pass

                    # Send success confirmation
                    success_embed = discord.Embed(
                        title=f'{self.bot.emotes.tick_icon} Name Updated!',
                        description=f'Your hub has been renamed from **{old_name}** to **{new_name}**.',
                        color=discord.Color.green(),
                    )
                    success_embed.add_field(
                        name='Note',
                        value='⚠️ Hub names can only be changed once every 10 days.',
                        inline=False,
                    )
                    await interaction.followup.send(embed=success_embed, ephemeral=True)

        except Exception as e:
            await interaction.followup.send(f'An error occurred: {str(e)}', ephemeral=True)

    async def edit_welcome(self, interaction: discord.Interaction['Bot']):
        """Handle welcome message editing with modal."""
        # Get current hub state
        db_hub = await get_hub_from_db(self.bot, self.hub.id)
        if not db_hub:
            return await interaction.response.send_message(HUB_NOT_FOUND_MSG, ephemeral=True)

        # Configure modal
        modal_config = [
            (
                'welcome_message',
                discord.ui.TextInput(
                    label='Welcome Message',
                    placeholder='Welcome to our hub! We hope you enjoy your stay...',
                    style=discord.TextStyle.paragraph,
                    max_length=500,
                    required=False,
                    default=db_hub.welcomeMessage or '',
                ),
            ),
        ]

        async def update_welcome(modal, hub):
            welcome_value = modal.saved_items['welcome_message'].value.strip()
            new_welcome = welcome_value if welcome_value else None
            success = await update_hub_in_db(self.bot, self.hub.id, welcomeMessage=new_welcome)
            if success:
                self.hub.welcomeMessage = new_welcome
                # Refresh UI
                updated_embed = await self.create_general_settings_embed()
                self.refresh_view_state()
                try:
                    await interaction.edit_original_response(embed=updated_embed, view=self)
                except discord.NotFound:
                    pass
            return success

        # Use generic handler
        await handle_modal_edit(
            interaction,
            self.bot,
            self.hub.id,
            'Edit Welcome Message',
            modal_config,
            update_welcome,
            'Welcome Message Updated!',
            'Your hub welcome message has been successfully updated.',
        )

    async def handle_toggle_nsfw(self, interaction: discord.Interaction['Bot']):
        """Handle NSFW toggle from select menu."""
        try:
            # Get current hub state
            db_hub = await get_hub_from_db(self.bot, self.hub.id)
            if not db_hub:
                return await interaction.response.send_message(HUB_NOT_FOUND_MSG, ephemeral=True)

            # Toggle NSFW status
            new_nsfw_status = not db_hub.nsfw

            # Update in database
            success = await update_hub_in_db(self.bot, self.hub.id, nsfw=new_nsfw_status)
            if not success:
                return await interaction.response.send_message(
                    'Failed to update hub settings.', ephemeral=True
                )

            # Update local hub object
            self.hub.nsfw = new_nsfw_status

            # Refresh the embed with updated data
            updated_embed = await self.create_general_settings_embed()
            self.refresh_view_state()

            # Update the original message with new embed and view
            await interaction.response.edit_message(embed=updated_embed, view=self)

            # Send success confirmation
            status_text = '🔞 NSFW' if new_nsfw_status else '✅ Safe for Work'
            success_embed = create_success_embed(
                self.bot,
                'NSFW Setting Updated!',
                f'Your hub is now marked as: **{status_text}**',
            )

            if new_nsfw_status:
                success_embed.add_field(
                    name='⚠️ Important',
                    value='Your hub is now marked as NSFW. This means it may contain adult content and will be filtered from searches by users who have disabled NSFW content.',
                    inline=False,
                )
            else:
                success_embed.add_field(
                    name='✅ Family Friendly',
                    value='Your hub is now marked as safe for work and will be visible to all users.',
                    inline=False,
                )

            await interaction.followup.send(embed=success_embed, ephemeral=True)

        except Exception as e:
            await interaction.response.send_message(f'An error occurred: {str(e)}', ephemeral=True)

    async def handle_toggle_private(self, interaction: discord.Interaction['Bot']):
        """Handle private toggle from select menu."""
        try:
            # Get current hub state
            db_hub = await get_hub_from_db(self.bot, self.hub.id)
            if not db_hub:
                return await interaction.response.send_message(HUB_NOT_FOUND_MSG, ephemeral=True)

            # Toggle private status
            new_private_status = not db_hub.private

            # Update in database
            success = await update_hub_in_db(self.bot, self.hub.id, private=new_private_status)
            if not success:
                return await interaction.response.send_message(
                    'Failed to update hub settings.', ephemeral=True
                )

            # Update local hub object
            self.hub.private = new_private_status

            status_text = '🔒 Private' if new_private_status else '🌐 Public'
            success_embed = create_success_embed(
                self.bot,
                'Visibility Updated!',
                f'Your hub is now: **{status_text}**',
            )

            if new_private_status:
                success_embed.add_field(
                    name='🔒 Private Hub',
                    value='Your hub is now private and can only be joined through invites. It will not appear in public searches.',
                    inline=False,
                )
            else:
                success_embed.add_field(
                    name='🌐 Public Hub',
                    value='Your hub is now public and discoverable. Users can find and join it without an invite.',
                    inline=False,
                )

            # Refresh the main embed with updated information
            updated_embed = await self.create_general_settings_embed()
            await interaction.response.edit_message(embed=updated_embed, view=self)
            await interaction.followup.send(embed=success_embed, ephemeral=True)

        except Exception as e:
            await interaction.response.send_message(f'An error occurred: {str(e)}', ephemeral=True)
