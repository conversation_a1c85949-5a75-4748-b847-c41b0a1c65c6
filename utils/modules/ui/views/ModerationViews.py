from collections.abc import Sequence
from datetime import datetime
import discord
from discord.ui import View, button, But<PERSON>, Select, Modal, TextInput
from typing import Optional, TYPE_CHECKING, TypedDict

from sqlalchemy import select

from utils.modules.core.db.models import (
    Hu<PERSON>,
    Hu<PERSON>Rep<PERSON>,
    Message,
    ReportStatus,
    Appeal,
    AppealStatus,
    Infraction,
)
from utils.modules.core.checks import interaction_check, is_interchat_staff_direct
from utils.modules.core.i18n import t
from utils.modules.core.moderation import mod_panel_embed, get_user_moderated_hubs
from utils.constants import logger
from utils.utils import parse_duration

if TYPE_CHECKING:
    from main import Bot

MAX_SELECT_OPTIONS = 25
DEFAULT_VIEW_TIMEOUT = 300
MAX_REASON_LENGTH = 400
MIN_REASON_LENGTH = 3
MAX_DURATION_LENGTH = 20
MIN_DURATION_LENGTH = 1
MAX_DESCRIPTION_LENGTH = 100

PLACEHOLDER_REPUTATION = 69
PLACEHOLDER_INFRACTIONS = 69


class DiscordTargetResolver:
    """Utility class for resolving Discord objects from IDs."""

    @staticmethod
    async def resolve_user(
        client: discord.Client, user_id: str, server_id: Optional[str] = None
    ) -> Optional[discord.User | discord.Member]:
        """Resolve a user or member from IDs."""
        try:
            if server_id:
                guild = client.get_guild(int(server_id))
                if guild:
                    member = guild.get_member(int(user_id))
                    if member:
                        return member
                    try:
                        return await guild.fetch_member(int(user_id))
                    except discord.NotFound:
                        pass
            return await client.fetch_user(int(user_id))
        except (ValueError, discord.NotFound, discord.HTTPException):
            return None

    @staticmethod
    def resolve_server(client: discord.Client, server_id: str) -> Optional[discord.Guild]:
        """Resolve a guild from its ID."""
        try:
            return client.get_guild(int(server_id))
        except ValueError:
            return None

    @staticmethod
    async def resolve_message(
        client: discord.Client, channel_id: str, message_id: str
    ) -> Optional[discord.Message]:
        """Resolve a message from channel and message IDs."""
        try:
            channel = client.get_channel(int(channel_id))
            if not channel:
                channel = await client.fetch_channel(int(channel_id))

            if isinstance(
                channel,
                (
                    discord.TextChannel,
                    discord.Thread,
                    discord.VoiceChannel,
                    discord.StageChannel,
                    discord.DMChannel,
                ),
            ) and hasattr(channel, 'fetch_message'):
                return await channel.fetch_message(int(message_id))
        except (ValueError, discord.NotFound, discord.HTTPException):
            pass
        return None


class BaseModerationView(View):
    def __init__(
        self,
        bot: 'Bot',
        moderator: discord.User | discord.Member,
        locale: str = 'en',
        timeout: float = DEFAULT_VIEW_TIMEOUT,
    ):
        super().__init__(timeout=timeout)
        self.bot = bot
        self.moderator = moderator
        self.locale = locale
        self.constants = bot.constants

    async def validate_interaction(
        self, interaction: discord.Interaction, expected_user: Optional[discord.abc.User] = None
    ) -> bool:
        """Validate that the interaction is from the expected user."""
        target_user = expected_user or self.moderator
        return await interaction_check(interaction, target_user, interaction.user)  # type: ignore[arg-type]

    async def send_error(self, interaction: discord.Interaction, message: str) -> None:
        """Send an error message to the user."""
        error_msg = f'{self.bot.emotes.x_icon} {message}'
        if interaction.response.is_done():
            await interaction.followup.send(error_msg, ephemeral=True)
        else:
            await interaction.response.send_message(error_msg, ephemeral=True)

    async def send_success(self, interaction: discord.Interaction, message: str) -> None:
        """Send a success message to the user."""
        success_msg = f'{self.bot.emotes.tick} {message}'
        if interaction.response.is_done():
            await interaction.followup.send(success_msg, ephemeral=True)
        else:
            await interaction.response.send_message(success_msg, ephemeral=True)


class HubSelectionView(BaseModerationView):
    """View for selecting a hub when no message is provided.

    This view is displayed when a moderator has access to multiple hubs
    and needs to select which hub they want to moderate.
    """

    def __init__(
        self,
        bot: 'Bot',
        moderator: discord.User | discord.Member,
        target_user: Optional[discord.User | discord.Member],
        target_server: Optional[discord.Guild],
        target_message: Optional[discord.Message],
        user_hubs: Sequence[Hub],
        constants,
        locale: str,
    ):
        super().__init__(bot, moderator, locale)
        self.target_user = target_user
        self.target_server = target_server
        self.target_message = target_message
        self.user_hubs = user_hubs

        # Add hub selection dropdown
        self.add_item(HubSelectDropdown(self.user_hubs, self))

    async def update_with_hub(self, interaction: discord.Interaction, selected_hub: Hub):
        """Update the view after a hub is selected - show the mod panel."""
        # Create the main mod panel
        view = ModPanelView(
            self.bot,
            self.moderator,
            self.target_user,
            self.target_server,
            self.target_message,
            selected_hub,
            self.constants,
            self.locale,
        )

        # Create embed
        embed = mod_panel_embed(
            self.bot,
            selected_hub,
            self.target_user,
            self.target_server,
            self.target_message,
            user_reputation=PLACEHOLDER_REPUTATION,
            user_infractions=PLACEHOLDER_INFRACTIONS,
            server_infractions=PLACEHOLDER_INFRACTIONS,
            _locale=self.locale,
        )

        await interaction.response.edit_message(embed=embed, view=view)


class HubSelectDropdown(Select):
    """
    Dropdown component for selecting a hub from available options.

    This dropdown is used within HubSelectionView to allow moderators
    to choose which hub they want to moderate.
    """

    def __init__(self, hubs: Sequence[Hub], parent_view: HubSelectionView):
        """Initialize the hub selection dropdown."""
        options = []
        for hub in hubs[:MAX_SELECT_OPTIONS]:  # Discord limit
            # Localize description with fallback
            description = hub.shortDescription or t(
                'ui.common.noDescription', locale=parent_view.locale
            )
            if len(description) > MAX_DESCRIPTION_LENGTH:
                description = description[:MAX_DESCRIPTION_LENGTH]

            options.append(
                discord.SelectOption(
                    label=hub.name,
                    value=hub.id,
                    description=description,
                )
            )

        super().__init__(
            placeholder=t('ui.moderation.hubSelect.placeholder', locale=parent_view.locale),
            options=options,
            min_values=1,
            max_values=1,
        )
        self.hubs = hubs
        self.parent_view = parent_view

    async def callback(self, interaction: discord.Interaction):
        """Handle hub selection from the dropdown."""
        if not await self.parent_view.validate_interaction(interaction):
            return

        selected_hub_id = self.values[0]
        selected_hub = next((hub for hub in self.hubs if hub.id == selected_hub_id), None)

        if selected_hub:
            await self.parent_view.update_with_hub(interaction, selected_hub)
        else:
            await self.parent_view.send_error(
                interaction,
                t(
                    'responses.moderation.errors.selectedHubNotFound',
                    locale=self.parent_view.locale,
                ),
            )


class ModPanelView(BaseModerationView):
    """Main moderation panel with action selection.

    This is the primary interface for moderation actions. It displays
    information about the target and provides a dropdown to select
    what action to take.
    """

    def __init__(
        self,
        bot: 'Bot',
        moderator: discord.User | discord.Member,
        target_user: Optional[discord.User | discord.Member],
        target_server: Optional[discord.Guild],
        target_message: Optional[discord.Message],
        selected_hub: Hub,
        constants,
        locale: str,
    ):
        super().__init__(bot, moderator, locale)
        self.target_user = target_user
        self.target_server = target_server
        self.target_message = target_message
        self.selected_hub = selected_hub

        self.add_item(ModActionSelect(self))

    async def handle_action_selection(self, interaction: discord.Interaction, action: str):
        """Handle when a moderation action is selected."""
        # Check if we need target selection (both user and server available)
        if self._needs_target_selection():
            await self._show_target_selection(interaction, action)
        else:
            # Open reason modal directly
            await self._open_reason_modal(interaction, action)

    def _needs_target_selection(self) -> bool:
        return self.target_user is not None and self.target_server is not None

    async def _show_target_selection(self, interaction: discord.Interaction, action: str):
        """Show the target selection view."""
        # Type guards: At this point we know both are not None
        assert self.target_user is not None
        assert self.target_server is not None

        view = TargetSelectionView(
            self.bot,
            self.moderator,
            self.target_user,
            self.target_server,
            self.target_message,
            self.selected_hub,
            action,
            self.locale,
        )

        embed = discord.Embed(
            title=t('ui.moderation.targetSelection.title', locale=self.locale),
            description=t(
                'ui.moderation.targetSelection.description', locale=self.locale, action=action
            ),
            color=discord.Color.blue(),
        )
        embed.add_field(
            name=t('ui.moderation.targetSelection.userField', locale=self.locale),
            value=f'{self.target_user.mention} (`{self.target_user.id}`)',
            inline=True,
        )
        embed.add_field(
            name=t('ui.moderation.targetSelection.serverField', locale=self.locale),
            value=f'**{self.target_server.name}** (`{self.target_server.id}`)',
            inline=True,
        )

        await interaction.response.send_message(embed=embed, view=view, ephemeral=True)

    async def _open_reason_modal(
        self, interaction: discord.Interaction, action: str, target_type: Optional[str] = None
    ):
        """Open the reason capture modal before executing moderation action."""
        modal = ReasonModal(self, action, target_type)
        await interaction.response.send_modal(modal)


class ModActionSelect(Select):
    """Select dropdown for moderation actions, updated for unified handler & added revoke actions."""

    def __init__(self, parent_view: ModPanelView):
        self.parent_view = parent_view
        options = self._build_action_options()

        super().__init__(
            placeholder=t('ui.moderation.actionSelect.placeholder', locale=self.parent_view.locale),
            options=options,
            min_values=1,
            max_values=1,
        )

    def _build_action_options(self) -> list[discord.SelectOption]:
        options = []
        emotes = self.parent_view.bot.emotes

        # Add delete option if message is provided
        if self.parent_view.target_message:
            options.append(
                discord.SelectOption(
                    emoji=emotes.delete_icon,
                    label=t('ui.moderation.actions.delete.label', locale='en'),
                    description=t('ui.moderation.actions.delete.description', locale='en'),
                    value='delete',
                )
            )

        # Standard punitive actions
        options.extend(
            [
                discord.SelectOption(
                    emoji=emotes.alert_icon,
                    label=t('ui.moderation.actions.warn.label', locale='en'),
                    description=t('ui.moderation.actions.warn.description', locale='en'),
                    value='warn',
                ),
                discord.SelectOption(
                    emoji=emotes.clock_icon,
                    label=t('ui.moderation.actions.mute.label', locale='en'),
                    description=t('ui.moderation.actions.mute.description', locale='en'),
                    value='mute',
                ),
                discord.SelectOption(
                    emoji=emotes.hammer_icon,
                    label=t('ui.moderation.actions.ban.label', locale='en'),
                    description=t('ui.moderation.actions.ban.description', locale='en'),
                    value='ban',
                ),
                # Revoke actions
                discord.SelectOption(
                    emoji=getattr(emotes, 'unmute_icon', None) or '🔊',
                    label=t('ui.moderation.actions.unmute.label', locale='en'),
                    description=t('ui.moderation.actions.unmute.description', locale='en'),
                    value='unmute',
                ),
                discord.SelectOption(
                    emoji=getattr(emotes, 'unban_icon', None) or '♻️',
                    label=t('ui.moderation.actions.unban.label', locale='en'),
                    description=t('ui.moderation.actions.unban.description', locale='en'),
                    value='unban',
                ),
            ]
        )

        # Add blacklist option for staff
        if is_interchat_staff_direct(self.parent_view.bot, str(self.parent_view.moderator.id)):
            options.append(
                discord.SelectOption(
                    emoji=emotes.hammer_icon,
                    label=t('ui.moderation.actions.blacklist.label', locale='en'),
                    description=t('ui.moderation.actions.blacklist.description', locale='en'),
                    value='blacklist',
                )
            )

        return options

    async def callback(self, interaction: discord.Interaction):
        """Handle action selection."""
        if not await self.parent_view.validate_interaction(interaction):
            return
        action = self.values[0]
        # All actions including blacklist proceed to modal (staff gating handled when option added)
        await self.parent_view.handle_action_selection(interaction, action)


class TargetSelectionView(BaseModerationView):
    """View for selecting between user and server targets after action selection."""

    def __init__(
        self,
        bot: 'Bot',
        moderator: discord.User | discord.Member,
        target_user: discord.User | discord.Member,
        target_server: discord.Guild,
        target_message: Optional[discord.Message],
        selected_hub: Hub,
        selected_action: str,
        locale: str,
    ):
        super().__init__(bot, moderator, locale)
        self.target_user = target_user
        self.target_server = target_server
        self.target_message = target_message
        self.selected_hub = selected_hub
        self.selected_action = selected_action

    @button(label='Act on User', style=discord.ButtonStyle.primary, emoji='👤')
    async def select_user(self, interaction: discord.Interaction['Bot'], button: Button):
        """Select user as the target."""
        if not await self.validate_interaction(interaction):
            return
        # Always open reason modal
        panel_wrapper = ModPanelView(
            self.bot,
            self.moderator,
            self.target_user,
            None,
            self.target_message,
            self.selected_hub,
            self.constants,
            self.locale,
        )
        await panel_wrapper._open_reason_modal(
            interaction, self.selected_action, target_type='user'
        )

    @button(label='Act on Server', style=discord.ButtonStyle.secondary, emoji='🏢')
    async def select_server(self, interaction: discord.Interaction['Bot'], button: Button):
        """Select server as the target."""
        if not await self.validate_interaction(interaction):
            return
        panel_wrapper = ModPanelView(
            self.bot,
            self.moderator,
            None,
            self.target_server,
            self.target_message,
            self.selected_hub,
            self.constants,
            self.locale,
        )
        await panel_wrapper._open_reason_modal(
            interaction, self.selected_action, target_type='server'
        )


class ReasonModal(Modal):
    """Collect reason (optional for unmute/unban) and optional duration for mute."""

    def __init__(self, parent_view: ModPanelView, action: str, target_type: Optional[str]):
        super().__init__(
            title=t('ui.moderation.modal.title', locale='en'), timeout=DEFAULT_VIEW_TIMEOUT
        )
        self.parent_view = parent_view
        self.action = action
        self.target_type = target_type

        self._setup_reason_input()
        self._setup_duration_input()

    def _setup_reason_input(self):
        """Setup the reason input field."""
        reason_required = self.action not in {'unmute', 'unban'}
        optional_suffix = '' if reason_required else ' (optional)'
        placeholder = t(
            'ui.moderation.modal.reason.placeholder',
            locale=self.parent_view.locale,
            optional=optional_suffix,
        )

        self.reason_input: TextInput = TextInput(
            label=t('ui.moderation.modal.reason.label', locale=self.parent_view.locale),
            placeholder=placeholder,
            style=discord.TextStyle.paragraph,
            min_length=0 if not reason_required else MIN_REASON_LENGTH,
            max_length=MAX_REASON_LENGTH,
            required=reason_required,
        )
        self.add_item(self.reason_input)

    def _setup_duration_input(self):
        """Setup the duration input field if needed."""
        if self.action == 'mute':
            self.duration_input: TextInput = TextInput(
                label=t('ui.moderation.modal.duration.label', locale=self.parent_view.locale),
                placeholder=t(
                    'ui.moderation.modal.duration.placeholder', locale=self.parent_view.locale
                ),
                style=discord.TextStyle.short,
                required=True,
                min_length=MIN_DURATION_LENGTH,
                max_length=MAX_DURATION_LENGTH,
            )
            self.add_item(self.duration_input)
        else:
            self.duration_input = None  # type: ignore

    async def on_submit(self, interaction: discord.Interaction['Bot']):  # type: ignore[override]
        """Handle form submission."""
        try:
            reason = self.reason_input.value.strip()
            duration_ms = await self._parse_duration_if_needed()

            if duration_ms is None and self.action == 'mute':
                return  # Error already sent

            await self._execute_moderation_action(interaction, reason, duration_ms)

        except Exception as e:
            await self.parent_view.send_error(
                interaction,
                t(
                    'responses.moderation.errors.processingFailed',
                    locale=self.parent_view.locale,
                    error=str(e),
                ),
            )

    async def _parse_duration_if_needed(self) -> Optional[int]:
        """Parse duration if this is a mute action."""
        if self.action != 'mute' or not self.duration_input:
            return None

        return parse_duration(self.duration_input.value.strip().lower())

    async def _execute_moderation_action(
        self, interaction: discord.Interaction['Bot'], reason: str, duration_ms: Optional[int]
    ):
        """Execute the moderation action."""
        from cogs.modules.moderation import (
            ModerationActionHandler,
            ActionType,
            ModerationTarget,
        )

        try:
            action_enum = ActionType(self.action)
        except ValueError:
            await self.parent_view.send_error(
                interaction,
                t('responses.moderation.errors.unknownAction', locale=self.parent_view.locale),
            )
            return

        user_target, server_target = self._resolve_targets()
        target = ModerationTarget(user=user_target, server=server_target)

        handler = ModerationActionHandler(
            self.parent_view.bot,
            self.parent_view.moderator,
            self.parent_view.selected_hub,
            self.parent_view.constants,
            self.parent_view.locale,
        )

        # Route to appropriate handler based on action type
        if action_enum == ActionType.DELETE and self.parent_view.target_message:
            await handler.handle_delete_message(
                interaction, self.parent_view.target_message, reason=reason or None
            )
        elif action_enum in {ActionType.WARN, ActionType.MUTE, ActionType.BAN}:
            await handler.handle_punitive_action(
                interaction, action_enum, target, reason or None, duration_ms=duration_ms
            )
        elif action_enum in {ActionType.UNMUTE, ActionType.UNBAN}:
            await handler.handle_revoke_action(interaction, action_enum, target)
        else:
            await self.parent_view.send_error(
                interaction,
                t('responses.moderation.errors.unsupportedAction', locale=self.parent_view.locale),
            )

    def _resolve_targets(
        self,
    ) -> tuple[Optional[discord.User | discord.Member], Optional[discord.Guild]]:
        """Resolve user and server targets based on target_type."""
        if self.target_type == 'user':
            return self.parent_view.target_user, None
        elif self.target_type == 'server':
            return None, self.parent_view.target_server
        else:
            # No target_type specified, use what's available
            if self.parent_view.target_user and not self.parent_view.target_server:
                return self.parent_view.target_user, None
            elif self.parent_view.target_server and not self.parent_view.target_user:
                return None, self.parent_view.target_server
            else:
                return self.parent_view.target_user, self.parent_view.target_server

    async def on_error(self, interaction: discord.Interaction, error: Exception) -> None:
        """Handle modal errors."""
        await self.parent_view.send_error(
            interaction, f'Failed to process moderation action: {error}'
        )


class ReportHandler:
    """Handler for report-related operations."""

    def __init__(self, bot: 'Bot'):
        self.bot = bot

    def parse_report_button_id(self, custom_id: str) -> Optional[str]:
        """Parse report ID from button custom_id."""
        parts = custom_id.split(':')

        if len(parts) != 3 or parts[0] != 'report':
            return None

        return parts[2] if parts[2] and parts[2] != '0' else None

    async def fetch_report_context(self, report_id: str) -> Optional[dict]:
        """Fetch report context from database."""
        async with self.bot.db.get_session() as session:
            stmt = (
                select(
                    HubReport.hubId,
                    HubReport.reporterId,
                    HubReport.reportedServerId,
                    HubReport.messageId,
                    Message.channelId,
                    HubReport.status,
                    HubReport.handledBy,
                )
                .join(Message, HubReport.messageId == Message.id, isouter=True)
                .where(HubReport.id == report_id)
            )
            result = await session.execute(stmt)
            row = result.first()

            if not row:
                return None

            return {
                'hub_id': row[0],
                'user_id': row[1],
                'server_id': row[2],
                'message_id': row[3],
                'channel_id': row[4],
                'status': row[5],
                'handled_by': row[6],
            }

    async def resolve_report_targets(
        self, context: dict
    ) -> tuple[
        Optional[discord.User | discord.Member], Optional[discord.Guild], Optional[discord.Message]
    ]:
        """Resolve Discord objects from report context."""
        target_user = None
        target_server = None
        target_message = None

        # Resolve user
        if context['user_id']:
            target_user = await DiscordTargetResolver.resolve_user(
                self.bot, context['user_id'], context['server_id']
            )

        # Resolve server
        if context['server_id']:
            target_server = DiscordTargetResolver.resolve_server(self.bot, context['server_id'])

        # Resolve message
        if context['channel_id'] and context['message_id']:
            target_message = await DiscordTargetResolver.resolve_message(
                self.bot, context['channel_id'], context['message_id']
            )

        return target_user, target_server, target_message

    async def update_report_status(
        self, report_id: str, new_status: ReportStatus, moderator_id: str
    ) -> bool:
        """Update report status in database."""
        try:
            async with self.bot.db.get_session() as session:
                stmt = select(HubReport).where(HubReport.id == report_id)
                result = await session.execute(stmt)
                report = result.scalar_one_or_none()

                if not report:
                    return False

                report.status = new_status
                report.handledBy = moderator_id
                from datetime import datetime

                report.handledAt = datetime.now()
                await session.commit()
                return True
        except Exception:
            return False


class ReportActionView(BaseModerationView):
    """Persistent report action view to open the moderation panel from a report log."""

    def __init__(
        self,
        bot: 'Bot',
        report_id: str,
        locale: str = 'en',
    ):
        super().__init__(bot, None, locale, timeout=None)  # type: ignore[arg-type]
        self.report_handler = ReportHandler(bot)

        self.open_custom_id = f'report:open_mod_panel:{report_id}'
        self.resolve_custom_id = f'report:resolve:{report_id}'
        self.ignore_custom_id = f'report:ignore:{report_id}'

        # Assign custom_ids to the actual buttons after View init
        self.open_mod_panel.custom_id = self.open_custom_id
        self.resolve_report.custom_id = self.resolve_custom_id
        self.ignore_report.custom_id = self.ignore_custom_id

        # Add emotes
        self.open_mod_panel.emoji = self.bot.emotes.hammer_icon
        self.resolve_report.emoji = self.bot.emotes.tick
        self.ignore_report.emoji = self.bot.emotes.x_icon

    @button(label='Open Mod Panel', style=discord.ButtonStyle.primary)
    async def open_mod_panel(self, interaction: discord.Interaction['Bot'], button: Button):
        """Open the moderation panel for this report."""
        # Parse report_id from the button's custom_id
        custom_id = interaction.data.get('custom_id', '') if interaction.data else ''
        report_id = self.report_handler.parse_report_button_id(custom_id)

        if not report_id:
            await self.send_error(
                interaction, t('responses.errors.missingAppealReference', locale=self.locale)
            )
            return

        try:
            await self._handle_open_mod_panel(interaction, report_id)
        except Exception as e:
            await self.send_error(
                interaction,
                t('responses.moderation.errors.openPanelFailed', locale=self.locale, error=str(e)),
            )

    async def _handle_open_mod_panel(self, interaction: discord.Interaction['Bot'], report_id: str):
        """Handle opening the moderation panel."""
        # Fetch context from DB
        context = await self.report_handler.fetch_report_context(report_id)
        if not context:
            await self.send_error(interaction, 'Report not found or has been deleted.')
            return

        # Resolve Discord targets
        (
            target_user,
            target_server,
            target_message,
        ) = await self.report_handler.resolve_report_targets(context)

        # Check moderation permissions
        user_hubs = await get_user_moderated_hubs(self.bot, str(interaction.user.id))
        if not user_hubs:
            await self.send_error(interaction, 'You do not moderate any hubs.')
            return

        # Show appropriate view based on number of hubs
        if len(user_hubs) == 1:
            await self._show_single_hub_panel(
                interaction, user_hubs[0], target_user, target_server, target_message
            )
        else:
            await self._show_hub_selection(
                interaction, user_hubs, target_user, target_server, target_message
            )

    async def _show_single_hub_panel(
        self,
        interaction: discord.Interaction['Bot'],
        hub: Hub,
        target_user: Optional[discord.User | discord.Member],
        target_server: Optional[discord.Guild],
        target_message: Optional[discord.Message],
    ):
        """Show moderation panel for a single hub."""
        view = ModPanelView(
            self.bot,
            interaction.user,
            target_user,
            target_server,
            target_message,
            hub,
            self.constants,
            self.locale,
        )

        embed = mod_panel_embed(
            self.bot,
            hub,
            target_user,
            target_server,
            target_message,
            user_reputation=PLACEHOLDER_REPUTATION,
            user_infractions=PLACEHOLDER_INFRACTIONS,
            server_infractions=PLACEHOLDER_INFRACTIONS,
            _locale=self.locale,
        )
        await interaction.response.send_message(embed=embed, view=view, ephemeral=True)

    async def _show_hub_selection(
        self,
        interaction: discord.Interaction['Bot'],
        user_hubs: Sequence[Hub],
        target_user: Optional[discord.User | discord.Member],
        target_server: Optional[discord.Guild],
        target_message: Optional[discord.Message],
    ):
        """Show hub selection view."""
        hub_select_view = HubSelectionView(
            self.bot,
            interaction.user,
            target_user,
            target_server,
            target_message,
            user_hubs,
            self.constants,
            self.locale,
        )
        await interaction.response.send_message(
            content=t('ui.moderation.hubSelection.prompt', locale=self.locale),
            view=hub_select_view,
            ephemeral=True,
        )

    @button(label='Resolve', style=discord.ButtonStyle.success)
    async def resolve_report(self, interaction: discord.Interaction['Bot'], button: Button):
        """Resolve the report."""
        await self._handle_report_resolution(interaction, action='resolve')

    @button(label='Ignore', style=discord.ButtonStyle.secondary)
    async def ignore_report(self, interaction: discord.Interaction['Bot'], button: Button):
        """Ignore the report."""
        await self._handle_report_resolution(interaction, action='ignore')

    async def _handle_report_resolution(self, interaction: discord.Interaction['Bot'], action: str):
        """Handle report resolution (resolve/ignore)."""
        custom_id = interaction.data.get('custom_id', '') if interaction.data else ''
        report_id = self.report_handler.parse_report_button_id(custom_id)

        if not report_id:
            await self.send_error(
                interaction, t('responses.errors.missingAppealReference', locale=self.locale)
            )
            return

        try:
            await self._process_report_resolution(interaction, report_id, action)
        except Exception as e:
            await self.send_error(
                interaction,
                t('responses.report.errors.processingFailed', locale=self.locale, error=str(e)),
            )

    async def _process_report_resolution(
        self, interaction: discord.Interaction['Bot'], report_id: str, action: str
    ):
        """Process the report resolution."""
        # Load report context for permissions and current status
        context = await self.report_handler.fetch_report_context(report_id)
        if not context:
            await self.send_error(
                interaction, t('responses.report.errors.notFoundOrDeleted', locale=self.locale)
            )
            return

        # Permission check: user must moderate this hub
        user_hubs = await get_user_moderated_hubs(self.bot, str(interaction.user.id))
        if not any(h.id == context['hub_id'] for h in user_hubs):
            await self.send_error(
                interaction, t('responses.moderation.errors.notModeratorForHub', locale=self.locale)
            )
            return

        # Check if already resolved/ignored
        current_status = context['status']
        if current_status in (ReportStatus.RESOLVED, ReportStatus.IGNORED):
            await self.send_error(
                interaction,
                t(
                    'responses.report.errors.alreadyHandled',
                    locale=self.locale,
                    status=current_status.value.lower(),
                ),
            )
            await self._refresh_report_view(
                interaction, report_id, current_status, interaction.user
            )
            return

        # Update status
        new_status = ReportStatus.RESOLVED if action == 'resolve' else ReportStatus.IGNORED
        success = await self.report_handler.update_report_status(
            report_id, new_status, str(interaction.user.id)
        )

        if not success:
            await self.send_error(
                interaction, t('responses.report.errors.updateFailed', locale=self.locale)
            )
            return

        # Update the view and confirm
        await self._refresh_report_view(interaction, report_id, new_status, interaction.user)
        action_label = t(
            'responses.report.success.actionPast',
            locale=self.locale,
            action=('resolved' if action == 'resolve' else 'ignored'),
        )
        await self.send_success(interaction, action_label)

        # Notify reporter via DM on resolution (privacy-preserving)
        if new_status == ReportStatus.RESOLVED:
            try:
                # Use context we already fetched
                reporter_id = context.get('user_id')
                if reporter_id:
                    user_obj = self.bot.get_user(int(reporter_id)) or await self.bot.fetch_user(
                        int(reporter_id)
                    )
                    if user_obj:
                        dm_message = t('responses.report.dm.resolved', locale=self.locale)
                        try:
                            await user_obj.send(dm_message)
                            logger.debug(
                                'DM sent to reporter_id=%s for report resolution', reporter_id
                            )
                        except discord.Forbidden:
                            logger.debug(
                                'Reporter_id=%s has DMs disabled; skipping DM', reporter_id
                            )
            except Exception:
                # Never fail the interaction due to DM issues
                pass

    async def _refresh_report_view(
        self,
        interaction: discord.Interaction['Bot'],
        report_id: str,
        status: ReportStatus,
        moderator: discord.abc.User,
    ):
        """Refresh the report view with updated status."""
        new_view = ReportActionView(self.bot, report_id=report_id, locale=self.locale)

        if status == ReportStatus.RESOLVED:
            new_view.resolve_report.label = t(
                'ui.report.status.resolvedBy', locale=self.locale, name=moderator.name
            )
            new_view.resolve_report.disabled = True
            new_view.remove_item(new_view.ignore_report)
        elif status == ReportStatus.IGNORED:
            new_view.ignore_report.label = t(
                'ui.report.status.ignoredBy', locale=self.locale, name=moderator.name
            )
            new_view.ignore_report.disabled = True
            new_view.remove_item(new_view.resolve_report)

        new_view.open_mod_panel.style = discord.ButtonStyle.secondary
        await interaction.response.edit_message(view=new_view)


class AppealContext(TypedDict):
    appeal_id: str
    user_id: str
    status: AppealStatus
    infraction_id: str
    hub_id: str
    infraction_type: str
    infraction_reason: str | None
    infraction_created_at: datetime


class AppealHandler:
    """Handler for appeal-related operations."""

    def __init__(self, bot: 'Bot'):
        self.bot = bot

    def parse_appeal_button_id(self, custom_id: str) -> Optional[str]:
        parts = custom_id.split(':')
        if len(parts) != 3 or parts[0] != 'appeal':
            return None
        return parts[2] if parts[2] and parts[2] != '0' else None

    async def fetch_appeal_context(self, appeal_id: str) -> Optional[AppealContext]:
        """Fetch appeal and related infraction context from DB."""
        async with self.bot.db.get_session() as session:
            stmt = (
                select(
                    Appeal.id,
                    Appeal.userId,
                    Appeal.status,
                    Appeal.infractionId,
                    Infraction.hubId,
                    Infraction.type,
                    Infraction.reason,
                    Infraction.createdAt,
                )
                .join(Infraction, Appeal.infractionId == Infraction.id)
                .where(Appeal.id == appeal_id)
            )
            result = await session.execute(stmt)
            row = result.first()
            if not row:
                return None
            return {
                'appeal_id': row[0],
                'user_id': row[1],
                'status': row[2],
                'infraction_id': row[3],
                'hub_id': row[4],
                'infraction_type': row[5],
                'infraction_reason': row[6],
                'infraction_created_at': row[7],
            }

    async def update_appeal_status(self, appeal_id: str, new_status: AppealStatus) -> bool:
        """Update appeal status in database."""
        try:
            async with self.bot.db.get_session() as session:
                stmt = select(Appeal).where(Appeal.id == appeal_id)
                res = await session.execute(stmt)
                appeal = res.scalar_one_or_none()
                if not appeal:
                    return False
                appeal.status = new_status
                await session.commit()
                return True
        except Exception:
            return False


class AppealDecisionModal(Modal, title=t('ui.appeal.actions.decisionTitle', locale='en')):
    """Modal to optionally collect decision reasoning."""

    def __init__(self, parent_view: 'AppealActionView', appeal_id: str, decision: str):
        super().__init__(timeout=DEFAULT_VIEW_TIMEOUT)
        self.parent_view = parent_view
        self.appeal_id = appeal_id
        self.decision = decision  # 'accept' | 'reject'
        self.locale = parent_view.locale

        self.reason_input: TextInput = TextInput(
            label=t('ui.appeal.actions.reasonOptional.label', locale=self.locale),
            placeholder=t('ui.appeal.actions.reasonOptional.placeholder', locale=self.locale),
            style=discord.TextStyle.paragraph,
            required=False,
            max_length=MAX_REASON_LENGTH,
        )
        self.add_item(self.reason_input)

    async def on_submit(self, interaction: discord.Interaction['Bot']):  # type: ignore[override]
        try:
            await self.parent_view.process_decision(
                interaction,
                appeal_id=self.appeal_id,
                decision=self.decision,
                reason=(self.reason_input.value or '').strip(),
            )
        except Exception as e:
            await self.parent_view.send_error(
                interaction,
                t(
                    'responses.appeal.errors.recordFailed',
                    locale=self.parent_view.locale,
                    error=str(e),
                ),
            )


class AppealActionView(BaseModerationView):
    """Appeal actions presented in the Appeals log channel."""

    def __init__(self, bot: 'Bot', appeal_id: str, locale: str = 'en'):
        super().__init__(bot, None, locale, timeout=None)  # type: ignore[arg-type]
        self.appeal_handler = AppealHandler(bot)

        # Set custom IDs for persistence
        self.accept_custom_id = f'appeal:accept:{appeal_id}'
        self.reject_custom_id = f'appeal:reject:{appeal_id}'
        self.view_infractions_custom_id = f'appeal:view_infractions:{appeal_id}'

        self.accept_appeal_button.custom_id = self.accept_custom_id
        self.reject_appeal_button.custom_id = self.reject_custom_id
        self.view_infractions_button.custom_id = self.view_infractions_custom_id

        # Emotes
        self.accept_appeal_button.emoji = self.bot.emotes.tick
        self.reject_appeal_button.emoji = self.bot.emotes.x_icon
        self.view_infractions_button.emoji = self.bot.emotes.hammer_icon

    @button(label='Accept Appeal', style=discord.ButtonStyle.success, row=0)
    async def accept_appeal_button(self, interaction: discord.Interaction['Bot'], button: Button):
        custom_id = interaction.data.get('custom_id', '') if interaction.data else ''
        appeal_id = self.appeal_handler.parse_appeal_button_id(custom_id)
        if not appeal_id:
            await self.send_error(
                interaction, t('responses.errors.missingAppealReference', locale='en')
            )
            return
        modal = AppealDecisionModal(self, appeal_id, decision='accept')
        await interaction.response.send_modal(modal)

    @button(label='Reject Appeal', style=discord.ButtonStyle.danger, row=0)
    async def reject_appeal_button(self, interaction: discord.Interaction['Bot'], button: Button):
        custom_id = interaction.data.get('custom_id', '') if interaction.data else ''
        appeal_id = self.appeal_handler.parse_appeal_button_id(custom_id)
        if not appeal_id:
            await self.send_error(
                interaction, t('responses.errors.missingAppealReference', locale=self.locale)
            )
            return
        modal = AppealDecisionModal(self, appeal_id, decision='reject')
        await interaction.response.send_modal(modal)

    @button(label='View User Infractions', style=discord.ButtonStyle.secondary, row=1)
    async def view_infractions_button(
        self, interaction: discord.Interaction['Bot'], button: Button
    ):
        custom_id = interaction.data.get('custom_id', '') if interaction.data else ''
        appeal_id = self.appeal_handler.parse_appeal_button_id(custom_id)
        if not appeal_id:
            await self.send_error(
                interaction, t('responses.errors.missingAppealReference', locale=self.locale)
            )
            return

        context = await self.appeal_handler.fetch_appeal_context(appeal_id)
        if not context:
            await self.send_error(
                interaction, t('responses.appeal.errors.notFoundOrDeleted', locale=self.locale)
            )
            return

        # Fetch recent infractions for the user (last 10)
        user_id = context['user_id']
        async with self.bot.db.get_session() as session:
            stmt = (
                select(Infraction)
                .where(Infraction.userId == user_id)
                .order_by(Infraction.createdAt.desc())
                .limit(10)
            )
            res = await session.execute(stmt)
            infractions = list(res.scalars().all())

        embed = discord.Embed(
            title=t('ui.appeal.viewInfractions.title', locale=self.locale),
            color=discord.Color.blurple(),
        )
        if not infractions:
            embed.description = t('ui.appeal.viewInfractions.empty', locale=self.locale)
        else:
            for inf in infractions:
                created_ts = int(inf.createdAt.timestamp()) if inf.createdAt else None
                created_str = (
                    f'<t:{created_ts}:R>'
                    if created_ts
                    else t('responses.common.unknown', locale=self.locale)
                )
                reason = inf.reason or t('responses.appeal.constants.noReason', locale=self.locale)
                if len(reason) > 120:
                    reason = reason[:117] + '...'
                embed.add_field(
                    name=f'{inf.type.name.title()} — {inf.status.name.title()}',
                    value=f'{t("ui.common.labels.date", locale=self.locale)}: {created_str}\n{t("ui.moderation.modal.reason.label", locale=self.locale)}: {reason}',
                    inline=False,
                )

        # Always ephemeral to the moderator clicking
        if interaction.response.is_done():
            await interaction.followup.send(embed=embed, ephemeral=True)
        else:
            await interaction.response.send_message(embed=embed, ephemeral=True)

    async def process_decision(
        self,
        interaction: discord.Interaction['Bot'],
        appeal_id: str,
        decision: str,
        reason: str,
    ) -> None:
        # Validate appeal exists
        context = await self.appeal_handler.fetch_appeal_context(appeal_id)
        if not context:
            await self.send_error(
                interaction, t('responses.appeal.errors.notFoundOrDeleted', locale=self.locale)
            )
            return

        # Update status
        new_status = AppealStatus.ACCEPTED if decision == 'accept' else AppealStatus.REJECTED
        success = await self.appeal_handler.update_appeal_status(appeal_id, new_status)
        if not success:
            await self.send_error(
                interaction, t('responses.appeal.errors.updateFailed', locale=self.locale)
            )
            return

        # Update the view (disable both primary buttons and annotate who acted)
        new_view = AppealActionView(self.bot, appeal_id=appeal_id, locale=self.locale)
        actor = interaction.user
        if new_status == AppealStatus.ACCEPTED:
            new_view.accept_appeal_button.label = t(
                'ui.appeal.status.acceptedBy', locale=self.locale, name=actor.name
            )
            new_view.accept_appeal_button.disabled = True
            new_view.reject_appeal_button.disabled = True
        else:
            new_view.reject_appeal_button.label = t(
                'ui.appeal.status.rejectedBy', locale=self.locale, name=actor.name
            )
            new_view.reject_appeal_button.disabled = True
            new_view.accept_appeal_button.disabled = True

        await interaction.response.edit_message(view=new_view)

        # DM the appealing user with the decision
        try:
            user_obj = self.bot.get_user(int(context['user_id'])) or await self.bot.fetch_user(
                int(context['user_id'])
            )
            if user_obj:
                hub_name = None
                try:
                    # Fetch hub name to personalize message
                    async with self.bot.db.get_session() as session:
                        hub = await session.get(Hub, context['hub_id'])
                        hub_name = hub.name if hub else None
                except Exception:
                    hub_name = None

                hub_part = f' {hub_name}' if hub_name else ''
                if new_status == AppealStatus.ACCEPTED:
                    dm = t('responses.appeal.dm.accepted', locale=self.locale, hubName=hub_part)
                else:
                    dm = t('responses.appeal.dm.declined', locale=self.locale, hubName=hub_part)
                if reason:
                    dm += '\n\n' + t(
                        'responses.appeal.dm.moderatorNote', locale=self.locale, reason=reason
                    )
                await user_obj.send(dm)

        except Exception:
            pass
