from __future__ import annotations

from dataclasses import dataclass
from datetime import datetime
import re
from typing import Optional, TYPE_CHECKING

import discord
from discord.ui import View, Button, TextInput
from sqlalchemy import select
from sqlalchemy.exc import IntegrityError

from utils.constants import logger
from utils.modules.core.db.models import Hub
from utils.modules.ui.CustomModal import CustomModal

if TYPE_CHECKING:
    from main import Bot


NAME_RE = re.compile(r'^[\w \-]{2,50}$', re.IGNORECASE)


@dataclass
class _HubDraft:
    name: Optional[str] = None
    short_description: Optional[str] = None
    private: Optional[bool] = None  # True=Private, False=Public


class HubCreationView(View):
    def __init__(self, bot: Bot, user: discord.abc.User):
        super().__init__(timeout=300)
        self.bot: Bot = bot
        self.constants = bot.constants
        self.initiator: discord.abc.User = user
        self.message: Optional[discord.Message] = None
        self.draft = _HubDraft()

        # Initial button (Create)
        self.create_button = Button(
            label='Create', style=discord.ButtonStyle.primary, emoji=bot.emotes.wand_icon
        )
        self.create_button.callback = self._on_click_create
        self.add_item(self.create_button)

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        if interaction.user.id != self.initiator.id:
            await interaction.response.send_message("You can't use this interface.", ephemeral=True)
            return False
        return True

    # ========= Stage 1: Modal (name + short description) =========
    async def _on_click_create(self, interaction: discord.Interaction):
        name_input = TextInput(
            label='Hub name',
            placeholder='e.g. Cosmic Lounge',
            required=True,
            min_length=2,
            max_length=50,
        )
        short_desc_input = TextInput(
            label='Short description',
            placeholder='One-liner shown in listings (max 100 chars)',
            required=True,
            min_length=10,
            max_length=100,
            style=discord.TextStyle.paragraph,
        )

        modal = CustomModal(
            title='Create a Hub',
            options=[('hub_name', name_input), ('short_desc', short_desc_input)],
        )

        await interaction.response.send_modal(modal)
        submitted = not await modal.wait()
        if not submitted:
            return  # timed out / closed

        # Extract values
        hub_name = modal.saved_items['hub_name'].value.strip()
        short_desc = modal.saved_items['short_desc'].value.strip()

        # Validate inputs
        err = await self._validate_name_and_desc(hub_name, short_desc)
        if err:
            embed = discord.Embed(
                title='Invalid input',
                description=f'{self.bot.emotes.x_icon} {err}',
                color=discord.Color.red(),
            )
            return await interaction.followup.send(embed=embed, ephemeral=True)

        # Persist draft
        self.draft.name = hub_name
        self.draft.short_description = short_desc

        # Next stage: visibility selection
        await self._show_visibility_selection(interaction)

    async def _validate_name_and_desc(self, name: str, short_desc: str) -> Optional[str]:
        if not NAME_RE.match(name):
            return 'Hub name must be 2-50 characters and only contain letters, numbers, spaces, hyphens, or underscores.'
        if len(short_desc) > 100 or len(short_desc) < 10:
            return 'Short description must be between 10 and 100 characters.'

        # Ensure unique (case-insensitive)
        async with self.bot.db.get_session() as session:
            existing = (await session.execute(select(Hub).where(Hub.name.ilike(name)))).scalar()
            if existing:
                return 'A hub with that name already exists. Please pick another.'
        return None

    # ========= Stage 2: Visibility selection =========
    async def _show_visibility_selection(self, interaction: discord.Interaction):
        embed = discord.Embed(
            description=(
                f'### {self.bot.emotes.house_icon} Select visibility\n'
                'Choose whether your hub is Public ([discoverable](https://interchat.tech/hubs)) or Private (invite-only).'
            ),
            color=self.constants.color(),
        )

        # Replace view with Public / Private buttons
        self.clear_items()
        public_btn = Button(
            label='Public', style=discord.ButtonStyle.success, emoji=self.bot.emotes.globe_icon
        )
        private_btn = Button(
            label='Private', style=discord.ButtonStyle.secondary, emoji=self.bot.emotes.lock_icon
        )
        public_btn.callback = self._on_select_public
        private_btn.callback = self._on_select_private
        self.add_item(public_btn)
        self.add_item(private_btn)

        await self._safe_edit(interaction, embed=embed, view=self)

    async def _on_select_public(self, interaction: discord.Interaction):
        self.draft.private = False
        await self._show_preview(interaction)

    async def _on_select_private(self, interaction: discord.Interaction):
        self.draft.private = True
        await self._show_preview(interaction)

    # ========= Stage 3: Preview + Confirm/Cancel =========
    async def _show_preview(self, interaction: discord.Interaction):
        name = self.draft.name or '(missing)'
        sdesc = self.draft.short_description or '(missing)'
        visibility = 'Private' if self.draft.private else 'Public'

        embed = discord.Embed(
            title='Preview',
            description=(
                f'**Name:** {name}\n**Description:** {sdesc}\n**Visibility:** {visibility}'
            ),
            color=self.constants.color(),
        )
        embed.set_footer(text='Confirm to create your hub or cancel to abort.')

        self.clear_items()
        confirm_btn = Button(
            label='Confirm', style=discord.ButtonStyle.success, emoji=self.bot.emotes.tick
        )
        cancel_btn = Button(
            label='Cancel', style=discord.ButtonStyle.danger, emoji=self.bot.emotes.x_icon
        )
        confirm_btn.callback = self._on_confirm
        cancel_btn.callback = self._on_cancel
        self.add_item(confirm_btn)
        self.add_item(cancel_btn)

        await self._safe_edit(interaction, embed=embed, view=self)

    # ========= Stage 4: Create or Cancel =========
    async def _on_confirm(self, interaction: discord.Interaction):
        # Re-validate (race conditions / uniqueness)
        assert self.draft.name and self.draft.short_description and self.draft.private is not None
        err = await self._validate_name_and_desc(self.draft.name, self.draft.short_description)
        if err:
            embed = discord.Embed(
                title='Unable to create hub',
                description=f'{self.bot.emotes.x_icon} {err}',
                color=discord.Color.red(),
            )
            return await interaction.response.send_message(embed=embed, ephemeral=True)

        # Persist
        hub_id: Optional[str] = None
        try:
            async with self.bot.db.get_session() as session:
                hub = Hub(
                    name=self.draft.name,
                    shortDescription=self.draft.short_description,
                    description=self.draft.short_description,
                    ownerId=str(interaction.user.id),
                    iconUrl='',
                    rules=[],
                    private=bool(self.draft.private),
                    createdAt=datetime.now(),
                    updatedAt=datetime.now(),
                    lastActive=datetime.now(),
                )
                session.add(hub)
                await session.commit()
                hub_id = hub.id
        except IntegrityError:
            # Likely unique constraint on hub name
            await interaction.response.send_message(
                embed=discord.Embed(
                    title='Unable to create hub',
                    description=f'{self.bot.emotes.x_icon} A hub with that name already exists. Please pick another.',
                    color=discord.Color.red(),
                ),
                ephemeral=True,
            )
            return
        except Exception as e:
            logger.error(f'Failed to create hub: {e}')
            embed = discord.Embed(
                title='Error',
                description=f'{self.bot.emotes.x_icon} Something went wrong while creating your hub. Please try again.',
                color=discord.Color.red(),
            )
            return await interaction.response.send_message(embed=embed, ephemeral=True)

        dashboard_url = f'https://interchat.tech/dashboard/hubs/{hub_id}'
        public_url = f'https://interchat.tech/hubs/{hub_id}'

        desc_lines = [
            f'### {self.bot.emotes.tick} Hub created!',
            f'Set icon & banner anytime via the [dashboard]({dashboard_url}) (recommended)',
        ]
        if self.draft.private is False:
            desc_lines.append(f'**{self.bot.emotes.wand_icon} Public page:** [View]({public_url})')
            desc_lines.append(
                '**Next Steps:** Connect servers to your hub using `/connect` or by visiting the public page!'
            )
        else:
            desc_lines.append(
                '**Next Steps:** Create an invite to your hub to add servers to the hub. (important)'
            )

        embed = discord.Embed(
            description='\n'.join(desc_lines),
            color=discord.Color.green(),
        )

        self.clear_items()  # end of flow
        await self._safe_edit(interaction, embed=embed, view=None)
        self.stop()

    async def _on_cancel(self, interaction: discord.Interaction):
        embed = discord.Embed(
            title='Creation canceled',
            description=f'{self.bot.emotes.x_icon} Abort Creation',
            color=discord.Color.red(),
        )
        self.clear_items()
        await self._safe_edit(interaction, embed=embed, view=None)
        self.stop()

    # ========= Helpers =========
    async def _safe_edit(
        self, interaction: discord.Interaction, *, embed: discord.Embed, view: Optional[View]
    ):
        try:
            if interaction.response.is_done():
                await interaction.edit_original_response(embed=embed, view=view)
            else:
                await interaction.response.edit_message(embed=embed, view=view)
        except discord.NotFound:
            if view:
                # Original message deleted; try sending a new ephemeral followup
                await interaction.followup.send(embed=embed, view=view, ephemeral=True)
