from typing import TYPE_CHECKING

import discord
from discord.ext import commands
from discord.ui import View, Button

from zuid import Z<PERSON><PERSON>
import sentry_sdk as sdk
from utils.constants import logger

from utils.modules.errors import customDiscord
from utils.modules.core.i18n import t
from utils.constants import InterchatConstants

if TYPE_CHECKING:
    from main import Bot

constants = InterchatConstants()


class ErrorButtons(View):
    def __init__(self, bot: 'Bot'):
        super().__init__()
        self.bot = bot

        self.add_item(
            Button(
                emoji=bot.emotes.code_icon,
                label='Support',
                url='https://discord.gg/8DhUA4HNpD',
                style=discord.ButtonStyle.link,
            )
        )
        self.add_item(
            Button(
                emoji=bot.emotes.wand_icon,
                label='Dashboard',
                url='https://interchat.tech/dashboard',
                style=discord.ButtonStyle.link,
            )
        )


async def error_handler(
    source: discord.Interaction['Bot'] | commands.Context['Bot'],
    error: Exception,
):
    original_error = error
    if isinstance(error, commands.CommandInvokeError):
        error = error.original
    elif isinstance(error, commands.HybridCommandError):
        error = error.original

    error_id = ZUID(prefix='error_', length=10)()
    embed = discord.Embed(
        title=t('responses.errors.errorTitle', locale='en'),
        description=' ',
        color=discord.Color.red(),
    )

    is_interaction = isinstance(source, discord.Interaction)
    user = source.user if is_interaction else source.author
    guild = source.guild

    bot = source.client if is_interaction else source.bot
    emotes = bot.emotes

    if isinstance(error, commands.MissingRequiredArgument):
        embed.description = t(
            'responses.errors.missingArgument',
            locale='en',
            x_icon=emotes.x_icon,
            param=error.param.name,
        )

    elif isinstance(error, commands.BadArgument):
        embed.description = f'{emotes.x_icon} Invalid input. Please try again.'

    elif isinstance(error, discord.NotFound):
        embed.description = f'{emotes.x_icon} Asset not found. Please check my permissions.'

    elif isinstance(error, discord.Forbidden):
        embed.description = f"{emotes.x_icon} I don't have permission to do that."

    elif isinstance(error, discord.HTTPException) and error.code == 10062:
        embed.description = f"{emotes.x_icon} Discord couldn't process that. Try again."

    elif isinstance(error, commands.MissingPermissions):
        embed.title = 'Insufficient Permissions'
        embed.description = f"{emotes.x_icon} You don't have permission to do this."

    elif isinstance(error, customDiscord.InteractionCheck):
        embed.description = f'{emotes.x_icon} You may not use this interaction.'

    elif isinstance(error, customDiscord.RateLimited):
        embed.description = f"{emotes.x_icon} You're being rate limited. Slow down."

    elif isinstance(error, customDiscord.WebhookRateLimit):
        embed.description = f'{emotes.x_icon} You have reached the webhook creation ratelimit. Please wait and try again soon.'

    elif isinstance(error, customDiscord.InvalidInput):
        embed.description = f'{emotes.x_icon} You have not provided valid input.'

    elif isinstance(error, ValueError):
        embed.description = (
            f'{emotes.x_icon} You have not provided valid input, was it the correct type?'
        )

    elif isinstance(error, customDiscord.InvalidInvite):
        embed.description = f'{emotes.x_icon} This invite is expired, or invalid. Please try again.'

    elif isinstance(error, customDiscord.WebhookError):
        embed.description = f'{emotes.x_icon} Uh oh! Something went wrong with our webhook service. Do I have the correct permissions?'

    elif isinstance(error, customDiscord.NotConnected):
        embed.description = f'{emotes.x_icon} I could not find a hub connected in this channel.'
    elif isinstance(error, customDiscord.NoInteraction):
        embed.description = f'{emotes.x_icon} {error.message}'

    elif isinstance(error, (commands.CheckFailure, commands.CommandNotFound)):
        return

    else:
        logger.error(f'Error in command: {error}')
        embed.description = t('responses.errors.whoops', locale='en', x_icon=emotes.x_icon)
        embed.add_field(name='Error ID', value=f'`{error_id}`', inline=True)

        if constants.environment().lower() == 'production':
            with sdk.push_scope() as scope:
                if guild:
                    scope.set_tag('guild_id', guild.id)
                scope.set_tag('error_id', error_id)
                scope.level = 'error'
                sdk.capture_exception(original_error)

        dev_embed = discord.Embed(title='Error', description=f'{error}', color=discord.Color.red())
        dev_embed.add_field(name='Error ID', value=f'```{error_id}```', inline=False)
        dev_embed.add_field(name='User', value=f'{user.mention} `{user.id}`', inline=False)

        if guild:
            dev_embed.add_field(name='Guild', value=f'{guild.name} `{guild.id}`', inline=False)

        if not is_interaction and hasattr(source, 'command'):
            dev_embed.add_field(name='Command', value=f'{source.command}', inline=False)

        dev_guild = bot.get_guild(1390620814478544946)
        if dev_guild:
            channel = discord.utils.get(dev_guild.text_channels, id=1390620815199965223)
            if channel:
                await channel.send(embed=dev_embed)

    view = ErrorButtons(bot)
    try:
        if is_interaction:
            if not source.response.is_done():
                await source.response.send_message(embed=embed, view=view, ephemeral=True)
            else:
                await source.followup.send(embed=embed, view=view, ephemeral=True)
        else:
            await source.send(embed=embed, view=view)
    except discord.Forbidden:
        try:
            await user.send(embed=embed, view=view)
        except Exception:
            pass
