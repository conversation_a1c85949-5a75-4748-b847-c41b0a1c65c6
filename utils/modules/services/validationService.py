from typing import TYPE_CHECKING

import discord

from utils.modules.broadcast.checks import Message<PERSON>alida<PERSON>, ValidationResult
from utils.modules.core.db.models import Hub
from utils.modules.services.BaseService import BaseService

if TYPE_CHECKING:
    from sqlalchemy.ext.asyncio import AsyncSession


class ValidationService(BaseService):
    """Service for message validation operations."""

    def __init__(self, session: 'AsyncSession'):
        super().__init__(session)
        self._validator = MessageValidator(self.session)

    async def validate_message_for_broadcast(
        self, message: discord.Message, hub: Hub, use_cache: bool = False
    ) -> 'ValidationResult':
        """
        Validate a message for broadcasting with caching support.
        """
        if not message.guild:
            return ValidationResult(is_valid=False, reason='Message not from a guild')

        user_id = str(message.author.id)
        guild_id = str(message.guild.id)

        return await self._validator.validate_message(message, hub, user_id, guild_id)
