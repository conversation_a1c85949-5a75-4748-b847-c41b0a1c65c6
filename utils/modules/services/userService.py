from typing import TYPE_CHECKING, List, Optional, Tuple
from sqlalchemy import select

from utils.modules.core.checks import is_interchat_staff_direct
from utils.modules.core.db.models import User, Badges
from utils.modules.services.BaseService import BaseService
from utils.modules.core.cache import user_badge_cache
from utils.utils import parse_discord_emoji
from data.badges import badges

if TYPE_CHECKING:
    from main import Bot
    from utils.modules.core.db.models import Badges
    from utils.interfaces import InterChatBadge


class UserService(BaseService):
    """Service for user management operations."""

    async def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get a user by their ID."""
        stmt = select(User).where(User.id == user_id)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def create_or_update_user(self, user_id: str, **kwargs) -> User:
        """Create a new user or update existing one."""
        user = await self.get_user_by_id(user_id)

        if user:
            # Update existing user
            for key, value in kwargs.items():
                if hasattr(user, key):
                    setattr(user, key, value)
        else:
            # Create new user
            user = User(id=user_id, **kwargs)
            self.session.add(user)

        await self.commit()
        return user

    async def fetch_badges(
        self,
        bot: 'Bot',
        user_id: str,
        use_cache: bool = True,
    ) -> Tuple[bool, List['InterChatBadge']]:
        if use_cache:
            cached_data = await user_badge_cache.get_user_badges(user_id)
            if cached_data:
                return cached_data

        badges_list: list['InterChatBadge'] = []

        if int(user_id) in bot.constants.auth_users():
            badges_list.append(
                {
                    'icon': str(bot.emotes.developer_badge),
                    'title': 'InterChat Developer',
                    'description': 'Core developer of InterChat',
                }
            )

        if is_interchat_staff_direct(bot, user_id):
            badges_list.append(
                {
                    'icon': str(bot.emotes.staff_badge),
                    'title': 'InterChat Staff',
                    'description': 'InterChat staff member',
                }
            )

        stmt = select(User.badges, User.showBadges).where(User.id == user_id).limit(1)
        res = (await self.session.execute(stmt)).tuples().first()

        show_badges = True
        if res:
            db_badges, show_badges = res
            for badge in db_badges:
                badge_info = badges.get(badge)
                icon_attr = getattr(bot.emotes, badge_info['icon'], None) if badge_info else None
                if icon_attr and badge_info:
                    badges_list.append(
                        {
                            'icon': parse_discord_emoji(icon_attr),
                            'title': badge_info['name'],
                            'description': badge_info['description'],
                        }
                    )

        if use_cache:
            await user_badge_cache.set_user_badges(user_id, show_badges, badges_list)

        return show_badges, badges_list

    async def add_badge(self, user_id: str, badge: Badges) -> bool:
        """Add a badge to a user."""
        user = await self.get_user_by_id(user_id)
        if not user:
            return False

        if not user.badges:
            user.badges = []

        if badge not in user.badges:
            user.badges.append(badge)
            await self.commit()

            # Clear cache
            await user_badge_cache.clear_user_badges(user_id)
            return True

        return False

    async def remove_badge(self, user_id: str, badge: Badges) -> bool:
        """Remove a badge from a user."""
        user = await self.get_user_by_id(user_id)
        if not user or not user.badges:
            return False

        if badge in user.badges:
            user.badges.remove(badge)
            await self.commit()

            # Clear cache
            await user_badge_cache.clear_user_badges(user_id)
            return True

        return False

    async def set_badge_visibility(self, user_id: str, show_badges: bool) -> bool:
        """Set whether user's badges should be shown."""
        user = await self.get_user_by_id(user_id)
        if not user:
            return False

        user.showBadges = show_badges
        await self.commit()

        # Clear cache
        await user_badge_cache.clear_user_badges(user_id)
        return True

    async def increment_message_count(self, user_id: str) -> bool:
        """Increment user's message count."""
        user = await self.get_user_by_id(user_id)
        if not user:
            return False

        user.messageCount = (user.messageCount or 0) + 1
        await self.commit()
        return True

    async def update_reputation(self, user_id: str, change: int) -> bool:
        """Update user's reputation score."""
        user = await self.get_user_by_id(user_id)
        if not user:
            return False

        user.reputation = (user.reputation or 0) + change
        await self.commit()
        return True
