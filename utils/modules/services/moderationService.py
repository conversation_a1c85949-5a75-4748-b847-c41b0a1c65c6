from typing import List, Optional
from datetime import datetime
from sqlalchemy import select, and_, or_

from utils.modules.core.db.models import (
    Infraction,
    InfractionType,
    InfractionStatus,
    Blacklist,
    ServerBlacklist,
)
from utils.modules.services.BaseService import BaseService
from utils.utils import ms_to_datetime

NO_REASON = 'No reason provided.'


class ModerationService(BaseService):
    """Service for moderation operations."""

    async def create_infraction(
        self,
        hub_id: str,
        mod_id: str,
        user_id: Optional[str] = None,
        server_id: Optional[str] = None,
        server_name: Optional[str] = None,
        infraction_type: InfractionType = InfractionType.BAN,
        reason: str = NO_REASON,
        duration_ms: Optional[int] = None,
    ) -> Infraction:
        """Create a new infraction."""
        # Duplicate prevention for BAN/MUTE (BAN used for mute per current model)
        if infraction_type == InfractionType.BAN:
            active = await self.get_active_infractions(hub_id, user_id=user_id, server_id=server_id)
            if any(inf.type == InfractionType.BAN for inf in active):
                raise ValueError('DUPLICATE_BAN_OR_MUTE')

        expires_at = None
        if duration_ms:
            expires_at = ms_to_datetime(duration_ms)

        infraction = Infraction(
            hubId=hub_id,
            userId=user_id,
            serverId=server_id,
            type=infraction_type,
            reason=reason,
            moderatorId=mod_id,
            status=InfractionStatus.ACTIVE,
            expiresAt=expires_at,
            serverName=server_name,
        )

        self.session.add(infraction)
        await self.commit()
        return infraction

    async def get_active_infractions(
        self, hub_id: str, user_id: Optional[str] = None, server_id: Optional[str] = None
    ) -> List[Infraction]:
        """Get active infractions for a user or server in a hub."""
        conditions = [
            Infraction.hubId == hub_id,
            Infraction.status == InfractionStatus.ACTIVE,
            or_(Infraction.expiresAt.is_(None), Infraction.expiresAt > datetime.now()),
        ]

        if user_id:
            conditions.append(Infraction.userId == user_id)
        if server_id:
            conditions.append(Infraction.serverId == server_id)

        stmt = select(Infraction).where(and_(*conditions))
        result = await self.session.execute(stmt)
        return list(result.scalars().all())

    async def revoke_infraction(self, infraction_id: str, moderator_id: str) -> bool:
        """Revoke an active infraction."""
        stmt = select(Infraction).where(Infraction.id == infraction_id)
        result = await self.session.execute(stmt)
        infraction = result.scalar_one_or_none()

        if not infraction:
            return False

        infraction.status = InfractionStatus.REVOKED

        await self.commit()
        return True

    async def delete_infraction(self, infraction_id: str) -> bool:
        """Delete an infraction."""
        stmt = select(Infraction).where(Infraction.id == infraction_id)
        result = await self.session.execute(stmt)
        infraction = result.scalar_one_or_none()

        if not infraction:
            return False

        await self.session.delete(infraction)
        await self.commit()
        return True

    async def is_user_banned(self, hub_id: str, user_id: str) -> bool:
        """Check if a user is banned from a hub."""
        infractions = await self.get_active_infractions(hub_id, user_id=user_id)
        return any(inf.type == InfractionType.BAN for inf in infractions)

    async def is_server_banned(self, hub_id: str, server_id: str) -> bool:
        """Check if a server is banned from a hub."""
        infractions = await self.get_active_infractions(hub_id, server_id=server_id)
        return any(inf.type == InfractionType.BAN for inf in infractions)

    async def create_blacklist_entry(
        self,
        user_id: str,
        mod_id: str,
        reason: str = NO_REASON,
        duration_ms: Optional[int] = None,
    ) -> Blacklist:
        """Create a global blacklist entry."""
        # Duplicate prevention
        if await self.is_user_blacklisted(user_id):
            raise ValueError('DUPLICATE_GLOBAL_BLACKLIST_USER')

        expires_at = None
        if duration_ms:
            expires_at = ms_to_datetime(duration_ms)

        blacklist = Blacklist(
            userId=user_id,
            reason=reason,
            moderatorId=mod_id,
            expiresAt=expires_at,
            createdAt=datetime.now(),
        )

        self.session.add(blacklist)
        await self.commit()
        return blacklist

    async def create_server_blacklist_entry(
        self,
        server_id: str,
        mod_id: str,
        reason: str = NO_REASON,
        duration_ms: Optional[int] = None,
    ) -> ServerBlacklist:
        """Create a global server blacklist entry."""
        if await self.is_server_blacklisted(server_id):
            raise ValueError('DUPLICATE_GLOBAL_BLACKLIST_SERVER')

        expires_at = None
        if duration_ms:
            expires_at = ms_to_datetime(duration_ms)

        blacklist = ServerBlacklist(
            serverId=server_id,
            reason=reason,
            moderatorId=mod_id,
            expiresAt=expires_at,
            createdAt=datetime.now(),
        )

        self.session.add(blacklist)
        await self.commit()
        return blacklist

    async def is_user_blacklisted(self, user_id: str) -> bool:
        """Check if a user is globally blacklisted."""
        stmt = select(Blacklist).where(
            and_(
                Blacklist.userId == user_id,
                or_(Blacklist.expiresAt.is_(None), Blacklist.expiresAt > datetime.now()),
            )
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none() is not None

    async def is_server_blacklisted(self, server_id: str) -> bool:
        """Check if a server is globally blacklisted."""
        stmt = select(ServerBlacklist).where(
            and_(
                ServerBlacklist.serverId == server_id,
                or_(
                    ServerBlacklist.expiresAt.is_(None), ServerBlacklist.expiresAt > datetime.now()
                ),
            )
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none() is not None

    async def delete_blacklist_entry(self, user_id: str) -> bool:
        """Delete all global blacklist entries for a user."""
        stmt = select(Blacklist).where(Blacklist.userId == user_id)
        result = await self.session.execute(stmt)
        rows = list(result.scalars().all())

        if not rows:
            return False

        for row in rows:
            await self.session.delete(row)
        await self.commit()
        return True

    async def delete_server_blacklist_entry(self, server_id: str) -> bool:
        """Delete global server blacklist entries for a server."""
        stmt = select(ServerBlacklist).where(ServerBlacklist.serverId == server_id)
        result = await self.session.scalar(stmt)

        if not result:
            return False

        await self.session.delete(result)
        await self.commit()
        return True
