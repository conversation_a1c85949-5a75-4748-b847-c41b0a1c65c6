from typing import List, Optional
from sqlalchemy import select, and_

from utils.modules.core.db.models import Hub, Connection, HubModerator
from utils.modules.services.BaseService import BaseService
from utils.modules.hub.utils import get_user_permission, HubPermissionLevel


class HubService(BaseService):
    """Service for hub management operations."""

    async def get_hub_by_id(self, hub_id: str) -> Optional[Hub]:
        """Get a hub by its ID."""
        stmt = select(Hub).where(Hub.id == hub_id)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def get_hub_by_name(self, hub_name: str, include_private: bool = False) -> Optional[Hub]:
        """Get a hub by its name."""
        conditions = [Hub.name == hub_name]
        if not include_private:
            conditions.append(Hub.private.is_(False))

        stmt = select(Hub).where(and_(*conditions))
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def get_user_hubs(self, user_id: str) -> List[Hub]:
        """Get all hubs where the user has permissions."""
        stmt = (
            select(Hub)
            .join(HubModerator, Hub.id == HubModerator.hubId)
            .where(HubModerator.userId == user_id)
        )
        result = await self.session.execute(stmt)
        return list(result.scalars().all())

    async def get_hub_connections(
        self, hub_id: str, connected_only: bool = True
    ) -> List[Connection]:
        """Get all connections for a hub."""
        conditions = [Connection.hubId == hub_id]
        if connected_only:
            conditions.append(Connection.connected.is_(True))

        stmt = select(Connection).where(and_(*conditions))
        result = await self.session.execute(stmt)
        return list(result.scalars().all())

    async def check_user_permission(self, hub_id: str, user_id: str) -> HubPermissionLevel:
        """Check user's permission level in a hub."""
        hub = await self.get_hub_by_id(hub_id)
        if not hub:
            return HubPermissionLevel.NONE

        return get_user_permission(hub, user_id)

    async def is_hub_locked(self, hub_id: str) -> bool:
        """Check if a hub is locked."""
        hub = await self.get_hub_by_id(hub_id)
        return hub.locked if hub else True

    async def lock_hub(self, hub_id: str, user_id: str) -> bool:
        """Lock a hub (requires appropriate permissions)."""
        permission_level = await self.check_user_permission(hub_id, user_id)
        if permission_level < HubPermissionLevel.MANAGER:
            return False

        hub = await self.get_hub_by_id(hub_id)
        if not hub:
            return False

        hub.locked = True
        await self.commit()
        return True

    async def unlock_hub(self, hub_id: str, user_id: str) -> bool:
        """Unlock a hub (requires appropriate permissions)."""
        permission_level = await self.check_user_permission(hub_id, user_id)
        if permission_level < HubPermissionLevel.MANAGER:
            return False

        hub = await self.get_hub_by_id(hub_id)
        if not hub:
            return False

        hub.locked = False
        await self.commit()
        return True

    async def update_hub_activity(self, hub_id: str) -> bool:
        """Update hub's last activity timestamp."""
        from datetime import datetime

        hub = await self.get_hub_by_id(hub_id)
        if not hub:
            return False

        hub.lastActive = datetime.now()
        await self.commit()
        return True
