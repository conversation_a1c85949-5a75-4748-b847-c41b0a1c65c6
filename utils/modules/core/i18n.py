from typing import Optional

from utils.modules.ui.Localization import get_text as _get_text
from utils.utils import load_user_locale, discordInteraction


def t(key: str, locale: str = 'en', **kwargs) -> str:
    """Translate a key using the given locale using Dot-notation paths.

    Example:
        t("commands.help.name", locale="en")
    """
    return _get_text(locale, key, **kwargs)


async def tu(
    bot, source: discordInteraction, key: str, *, locale: Optional[str] = None, **kwargs
) -> str:
    """Translate a key using the user's locale derived from the given source (ctx or interaction).

    If a locale is explicitly provided, it is used; otherwise, the user's saved locale
    is loaded from the database with fallback to 'en'.
    """
    lang = locale or await load_user_locale(bot, source)
    return _get_text(lang, key, **kwargs)


# Convenience alias
translate = t
translate_user = tu
