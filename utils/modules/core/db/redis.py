from utils.constants import redis_client, logger
from redis.exceptions import RedisError


async def validate_redis_connection():
    try:
        response = await redis_client.ping()
        if response:
            logger.info('Initialized Redis successfully')
        else:
            logger.critical('Redis ping returned falsy; stopping bot.')
            raise SystemExit(1)
    except RedisError as e:
        logger.critical(f'Failed to connect to Redis: {e}', exc_info=True)
        raise SystemExit(1)
