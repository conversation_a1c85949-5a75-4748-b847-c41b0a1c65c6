from typing import Optional
import discord
from discord.abc import GuildChannel
from sqlalchemy import select

from utils.modules.core.i18n import t
from utils.modules.core.db.models import Connection
from utils.modules.core.rateLimit import webhook_rate_limit

from typing import TYPE_CHECKING, Tuple, List

if TYPE_CHECKING:
    from main import Bot

async def get_webhook(bot: 'Bot', channel: GuildChannel) -> Optional[discord.Webhook]:
    """
    Get the webhook for a channel, or None if not found.

    Args:
        bot: The bot instance.
        channel: The channel to get the webhook for. Threads are not supported.
    """
    if not isinstance(channel, (discord.TextChannel, discord.ForumChannel)):
        return None

    webhooks = await channel.webhooks()
    for webhook in webhooks:
        if bot.user and webhook.user and (webhook.user.id == bot.user.id):
            return webhook
    return None


async def fetch_or_create_webhook(bot: 'Bot', channel: GuildChannel) -> Optional[discord.Webhook]:
    """Get or create a webhook for the specified channel."""
    if isinstance(channel, discord.Thread):
        parent = channel.parent
        if parent:
            webhook = await get_webhook(bot, parent)
            return webhook or await parent.create_webhook(name='InterChat Core 1')
        return None

    elif isinstance(channel, (discord.TextChannel)):
        webhook = await get_webhook(bot, channel)
        return webhook or await channel.create_webhook(name='InterChat Core 1')

    return None


async def get_and_cleanup_webhooks(bot: 'Bot', channel: GuildChannel):
    """
    Get all webhooks for a channel and delete the bot's webhook if it exists.
    """

    parent = channel.parent if isinstance(channel, discord.Thread) else channel

    if not isinstance(parent, (discord.TextChannel, discord.ForumChannel)):
        return

    try:
        webhooks = await parent.webhooks()
    except Exception:
        return

    for webhook in webhooks:
        if bot.user and webhook.user and webhook.user.id == bot.user.id:
            try:
                await webhook.delete()
            except Exception:
                pass


async def validate_webhook(bot: 'Bot', channel: GuildChannel) -> Optional[discord.Webhook]:
    """
    Validate the webhook for a channel, re-creating it if necessary.
    Args:
        bot: The bot instance.
        channel: The channel to validate the webhook for.
    Returns:
        The webhook if it exists or is created, None if the channel is not suitable.
    """

    webhook = await get_webhook(bot, channel)

    if not webhook:
        await webhook_rate_limit(channel)
        target = channel.parent if isinstance(channel, discord.Thread) else channel

        if isinstance(target, (discord.TextChannel, discord.ForumChannel)):
            try:
                webhook = await target.create_webhook(name='InterChat Core 1')
            except Exception:
                return None
        else:
            return None

    if not webhook:
        return None

    async with bot.db.get_session() as session:
        stmt = select(Connection).where(Connection.channelId == str(channel.id))
        result = (await session.execute(stmt)).scalar_one_or_none()

        if not result:
            return None

        result.webhookUrl = webhook.url
        await session.commit()

    return webhook


async def fix_connections(bot: 'Bot', guild: discord.Guild, locale) -> Tuple[List[Tuple[Connection, str]], List[Tuple[Connection, str]]]:
    user_error: List[Tuple[Connection, str]] = []
    fixed: List[Tuple[Connection, str]] = []

    async with bot.db.get_session() as session:
        stmt = select(Connection).where(Connection.serverId == str(guild.id))
        connections = (await session.execute(stmt)).scalars().all()

        for conn in connections:
            conn = await session.merge(conn)

            channel = guild.get_channel(int(conn.channelId))
            if not channel:
                await session.delete(conn)
                user_error.append((conn, f'{bot.emotes.trash_icon} {t("commands.connections.fix.responses.errors.channelDeleted", locale)}'))
                continue

            permissions = channel.permissions_for(guild.me)
            if not permissions.view_channel:
                user_error.append((conn, f'{bot.emotes.globe_icon} {t("commands.connections.fix.responses.errors.permissionsView", locale)}'))
                continue
            if not permissions.manage_webhooks:
                user_error.append((conn, f'{bot.emotes.link_icon} {t("commands.connections.fix.responses.errors.permissionsWebhook", locale)}'))
                continue
            if not permissions.send_messages:
                user_error.append((conn, f'{bot.emotes.chat_icon} {t("commands.connections.fix.responses.errors.permissionsSend", locale)}'))
                continue

            webhook = await get_webhook(bot, channel)

            if not webhook:
                await webhook_rate_limit(channel)
                target = channel.parent if isinstance(channel, discord.Thread) else channel

                webhook = await target.create_webhook(name='InterChat Core 1')
                conn.webhookURL = webhook.url
                fixed.append((conn, f'{bot.emotes.tick} {t("commands.connections.fix.responses.success.fixed", locale)}'))
            else:
                fixed.append((conn, f'{bot.emotes.tick} {t("commands.connections.fix.responses.success.valid", locale)}'))

        await session.commit()

    return fixed, user_error
