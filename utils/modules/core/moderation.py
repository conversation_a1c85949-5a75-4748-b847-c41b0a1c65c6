from typing import Optional, TYPE_CHECKING, <PERSON>ple
from discord import Embed
from sqlalchemy import select, and_, or_
from utils.constants import InterchatConstants
from utils.modules.core.db.models import (
    Hub,
    HubModerator,
    HubModeratorRole,
    Message,
    Broadcast,
    User,
)

if TYPE_CHECKING:
    import discord
    from sqlalchemy.ext.asyncio import AsyncSession
    from main import Bot

constants = InterchatConstants()


async def get_user_moderated_hubs(bot: 'Bot', user_id: str):
    """Get all hubs where the user has Moderator or Manager permissions."""
    async with bot.db.get_session() as session:
        stmt = (
            select(Hub)
            .join(HubModerator, Hub.id == HubModerator.hubId, isouter=True)
            .where(
                or_(
                    Hub.ownerId == user_id,
                    and_(
                        HubModerator.userId == user_id,
                        HubModerator.role.in_(
                            [HubModeratorRole.MODERATOR, HubModeratorRole.MANAGER]
                        ),
                    ),
                )
            )
        )
        result = await session.execute(stmt)
        return result.scalars().all()


async def fetch_original_message(session: 'AsyncSession', message_id: str) -> Optional[Message]:
    """
    Fetch original message, first directly then through broadcast relationship.

    This function implements the message priority logic:
    1. First, try to fetch the original message directly from the database
    2. If not found, fetch the broadcast record and get the original message through the relationship
    """
    # First try to fetch the message directly
    stmt = select(Message).where(Message.id == message_id)

    message = await session.scalar(stmt)

    if message:
        return message

    # If not found, try to fetch through broadcast relationship
    stmt = (
        select(Message)
        .join(Broadcast, Message.id == Broadcast.messageId)
        .where(Broadcast.id == message_id)
    )

    message = await session.scalar(stmt)
    return message


async def fetch_original_msg_with_extra(
    session: 'AsyncSession', message_id: str
) -> Optional[Tuple[Message, User, str]]:
    """
    Fetch original message, first directly then through broadcast relationship.

    This function implements the message priority logic:
    1. First, try to fetch the original message directly from the database
    2. If not found, fetch the broadcast record and get the original message through the relationship

    Returns:
        Tuple of (Message, User, Hub.id) if found, otherwise None
    """
    # First try to fetch the message directly
    stmt = (
        select(Message, User, Hub.id)
        .join(User, Message.authorId == User.id)
        .join(Hub, Message.hubId == Hub.id)
        .where(Message.id == message_id)
    )

    result = await session.execute(stmt)
    message = result.tuples().first()

    if message:
        return message

    # If not found, try to fetch through broadcast relationship
    stmt = (
        select(Message, User, Hub.id)
        .join(Broadcast, Message.id == Broadcast.messageId)
        .join(User, Message.authorId == User.id)
        .join(Hub, Message.hubId == Hub.id)
        .where(Broadcast.id == message_id)
    )
    result = await session.execute(stmt)
    message = result.tuples().first()

    if message:
        return message


def mod_panel_embed(
    bot: 'Bot',
    hub: Hub,
    target_user: Optional['discord.User | discord.Member'],
    target_server: Optional['discord.Guild'],
    target_message: Optional['discord.Message'],
    user_reputation: Optional[int],
    user_infractions: Optional[int],
    server_infractions: Optional[int],
    _locale: str,
) -> Embed:
    """Create the embed for the mod panel."""
    emotes = bot.emotes
    message_field_str = (
        f'> **[Message Content]({target_message.jump_url})** {target_message.content.replace("`", "")[:50]}\n'
        if target_message
        else ''
    )
    server_str = (
        f'> **Server:** {target_server.name} (`{target_server.id}`)\n' if target_server else ''
    )

    embed = Embed(
        title='Moderation Panel',
        description=' ',
        color=constants.color(),
    )

    embed.add_field(
        name=f'{emotes.chat_icon} Context',
        value=f'{message_field_str}{server_str}> **Hub:** {hub.name}',
        inline=False,
    )

    user_info_str = (
        f'> **User:** {target_user.name} (`{target_user.id}`)\n'
        f'> **User Infractions:** {user_infractions}\n'
        f'> **Reputation:** {user_reputation}\n'
        if target_user
        else ''
    )
    server_info_str = (
        f'> **Server:** {target_server.name} (`{target_server.id}`)\n'
        f'> **Owner:** <@{target_server.owner_id}>\n'
        f'> **Server Infractions:** {server_infractions}\n'
        if target_server
        else ''
    )
    embed.add_field(
        name=f'{emotes.info_icon} Target Information',
        value=f'{user_info_str}{server_info_str}',
        inline=False,
    )

    if target_user:
        embed.set_author(name=target_user.name, icon_url=target_user.display_avatar.url)
    elif target_server:
        embed.set_author(
            name=target_server.name,
            icon_url=target_server.icon.url if target_server.icon else None,
        )

    return embed
