from datetime import datetime, timed<PERSON><PERSON>
from typing import TYPE_CHECKING, Optional, NamedTuple
from utils.constants import logger, redis_client
from utils.modules.core.db.models import InfractionType

if TYPE_CHECKING:
    from sqlalchemy.ext.asyncio import AsyncSession
    from main import Bot
    from utils.modules.services.moderationService import ModerationService


class SpamDetectionResult(NamedTuple):
    is_spam: bool
    action_needed: Optional[str] = None  # 'warn' or 'mute'
    message_count: int = 0
    warning_message: Optional[str] = None


class AntiSpamManager:
    """Manages anti-spam detection and enforcement for hub messages"""

    # Anti-spam configuration
    RATE_LIMIT_MESSAGES = 5  # Max messages allowed in time window
    RATE_LIMIT_PERIOD = 5  # Time window in seconds
    BAN_DURATION_MINUTES = 10  # Mute duration for first offense
    WARNING_THRESHOLD = 2  # Warnings before mute
    # Redis key patterns
    MESSAGE_COUNT_KEY = 'spam_check:{user_id}:{hub_id}'
    WARNING_COUNT_KEY = 'spam_warnings:{user_id}:{hub_id}'

    def __init__(self, session: 'AsyncSession'):
        self.session = session
        self.redis = redis_client
        self.moderationsvc: 'ModerationService | None' = None
        self.set_moderation_service()

    def set_moderation_service(self):
        """Set the moderation service for spam actions."""
        if self.moderationsvc:
            return self.moderationsvc

        from utils.modules.services.moderationService import ModerationService

        self.moderationsvc = ModerationService(self.session)
        return self.moderationsvc

    def get_moderation_service(self) -> 'ModerationService':
        return self.moderationsvc or self.set_moderation_service()

    async def check_spam(self, user_id: str, hub_id: str) -> SpamDetectionResult:
        """
        Check if user is sending messages too quickly in a hub.
        Returns:
            SpamDetectionResult: Detection status and recommended action.
        """
        try:
            # Increment message count for this user in this hub
            message_count_key = self.MESSAGE_COUNT_KEY.format(user_id=user_id, hub_id=hub_id)
            message_count = await self.redis.incr(message_count_key)

            # Set expiration on first message
            if message_count == 1:
                await self.redis.expire(message_count_key, self.RATE_LIMIT_PERIOD)

            logger.debug(f'User {user_id} message count in hub {hub_id}: {message_count}')

            # Check if user exceeded rate limit
            if message_count > self.RATE_LIMIT_MESSAGES:
                # Check if user has already been warned
                warning_count = await self._get_warning_count(user_id, hub_id)

                if warning_count >= self.WARNING_THRESHOLD:
                    # User has been warned, escalate to mute
                    return SpamDetectionResult(
                        is_spam=True,
                        action_needed='mute',
                        message_count=message_count,
                        warning_message=f'You have been temporarily muted from this hub for 10 minutes due to spam. Your mute will expire at <t:{int((datetime.now() + timedelta(minutes=self.BAN_DURATION_MINUTES)).timestamp())}:F>.',
                    )
                else:
                    # First offense, issue warning
                    await self._increment_warning_count(user_id, hub_id)
                    return SpamDetectionResult(
                        is_spam=True,
                        action_needed='warn',
                        message_count=message_count,
                        warning_message='⚠️ You are sending messages too quickly! Please slow down or you will be temporarily muted.',
                    )

            # No spam detected
            return SpamDetectionResult(is_spam=False, message_count=message_count)

        except Exception as e:
            logger.error(f'Error during spam detection for user {user_id} in hub {hub_id}: {e}')
            # Fail safe - allow message if spam detection fails
            return SpamDetectionResult(is_spam=False)

    async def _get_warning_count(self, user_id: str, hub_id: str) -> int:
        """Get the current warning count for a user in a hub"""
        warning_key = self.WARNING_COUNT_KEY.format(user_id=user_id, hub_id=hub_id)
        try:
            count = await self.redis.get(warning_key)
            return int(count) if count else 0
        except Exception as e:
            logger.warning(f'Failed to get warning count for {user_id}: {e}')
            return 0

    async def _increment_warning_count(self, user_id: str, hub_id: str):
        """Increment warning count for a user in a hub with TTL"""
        warning_key = self.WARNING_COUNT_KEY.format(user_id=user_id, hub_id=hub_id)
        try:
            count = await self.redis.incr(warning_key)
            if count == 1:
                await self.redis.expire(warning_key, 3600)  # warnings expire after an hour
        except Exception as e:
            logger.warning(f'Failed to increment warning count for {user_id}: {e}')

    async def mute_user_for_spam(
        self, bot: 'Bot', user_id: str, hub_id: str, username: str = 'Unknown User'
    ) -> Optional[str]:
        """
        Mute a user for spam in a specific hub.
        Returns the infraction ID if successful, None otherwise.
        """
        try:
            # Use the bot's user ID as the moderator (since this is automated)
            bot_user_id = (
                str(bot.user.id) if bot.user else '0'
            )  # Fallback to "0" if bot.user is None
            reason = f'Auto-Mute: Spam detected - exceeded {self.RATE_LIMIT_MESSAGES} messages in {self.RATE_LIMIT_PERIOD} seconds'

            duration_ms = self.BAN_DURATION_MINUTES * 60 * 1000

            moderationsvc = self.get_moderation_service()
            # Create ban infraction
            infraction = await moderationsvc.create_infraction(
                hub_id=hub_id,
                mod_id=bot_user_id,
                user_id=user_id,
                reason=reason,
                infraction_type=InfractionType.BAN,
                duration_ms=duration_ms,
            )

            logger.info(f'Auto-muted user {user_id} ({username}) in hub {hub_id} for spam')

            # Clear warning count since user is now banned
            warning_key = self.WARNING_COUNT_KEY.format(user_id=user_id, hub_id=hub_id)
            await self.redis.delete(warning_key)

            return infraction.id if infraction else None

        except Exception as e:
            logger.error(f'Failed to mute user {user_id} for spam in hub {hub_id}: {e}')
            return None

    async def clear_spam_tracking(self, user_id: str, hub_id: str):
        """Clear spam tracking data for a user in a hub"""
        try:
            message_key = self.MESSAGE_COUNT_KEY.format(user_id=user_id, hub_id=hub_id)
            warning_key = self.WARNING_COUNT_KEY.format(user_id=user_id, hub_id=hub_id)
            await self.redis.delete(message_key, warning_key)
        except Exception as e:
            logger.warning(f'Failed to clear spam tracking for {user_id}: {e}')
