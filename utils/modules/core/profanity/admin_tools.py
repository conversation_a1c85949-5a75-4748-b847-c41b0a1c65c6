"""
Admin tools and utilities for managing profanity filter configurations.

Provides tools for:
- Bulk pattern entry and validation
- Import/export of filter configurations
- Pattern validation and testing
- Configuration management
"""

import csv
import re
from io import StringIO
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timezone

from sqlalchemy.orm import Session

from utils.modules.core.db.models import (
    AntiSwearRule,
    AntiSwearPattern,
    AntiSwearWhitelist,
    PatternMatchType,
    BlockWordAction,
    Hub,
)
from utils.modules.core.profanity.pattern_matcher import PatternMatcher


@dataclass
class ValidationResult:
    """Result of pattern validation."""

    valid: bool
    pattern: str
    match_type: PatternMatchType
    errors: List[str]
    warnings: List[str]


@dataclass
class BulkImportResult:
    """Result of bulk import operation."""

    total_patterns: int
    imported_patterns: int
    skipped_patterns: int
    errors: List[str]
    warnings: List[str]


@dataclass
class RuleExport:
    """Exportable rule configuration."""

    name: str
    enabled: bool
    actions: List[str]
    warning_threshold: int
    escalation_action: Optional[str]
    mute_duration_minutes: Optional[int]
    blacklist_duration_minutes: Optional[int]
    log_violations: bool
    patterns: List[Dict[str, str]]
    whitelist: List[str]


class PatternValidator:
    """Validates profanity filter patterns."""

    # Common problematic patterns that should be flagged
    PROBLEMATIC_PATTERNS = {
        r'^\*+$': 'Pattern contains only wildcards',
        r'^.{1,2}$': 'Pattern is too short (minimum 3 characters)',
        r'.*\s.*': 'Pattern contains whitespace',
        r'.*[<>{}[\]()\\|^$+?].*': 'Pattern contains regex special characters',
    }

    # Common words that might cause false positives
    FALSE_POSITIVE_WORDS = {
        'class',
        'classic',
        'glass',
        'grass',
        'pass',
        'bass',
        'mass',
        'assess',
        'assignment',
        'assistance',
        'associate',
        'assume',
        'scrap',
        'scrape',
        'script',
        'description',
        'prescription',
        'analysis',
        'analyst',
        'therapist',
        'specialist',
    }

    def __init__(self):
        self.pattern_matcher = PatternMatcher()

    def validate_pattern(self, pattern: str, match_type: PatternMatchType) -> ValidationResult:
        """
        Validate a single pattern.

        Args:
            pattern: Pattern to validate
            match_type: Type of pattern matching

        Returns:
            ValidationResult with validation details
        """
        errors = []
        warnings = []

        # Basic validation
        if not pattern or not pattern.strip():
            errors.append('Pattern cannot be empty')
            return ValidationResult(False, pattern, match_type, errors, warnings)

        pattern = pattern.strip()

        # Check for problematic patterns
        for regex, message in self.PROBLEMATIC_PATTERNS.items():
            if re.match(regex, pattern, re.IGNORECASE):
                errors.append(message)

        # Check pattern length
        if len(pattern) < 2:
            errors.append('Pattern must be at least 2 characters long')
        elif len(pattern) > 100:
            errors.append('Pattern is too long (maximum 100 characters)')

        # Check for potential false positives
        if match_type in [
            PatternMatchType.PREFIX,
            PatternMatchType.SUFFIX,
            PatternMatchType.WILDCARD,
        ]:
            false_positives = self._check_false_positives(pattern, match_type)
            if false_positives:
                warnings.append(f'May cause false positives with: {", ".join(false_positives[:5])}')

        # Test pattern compilation
        if not errors:
            if not self.pattern_matcher.add_pattern(pattern, match_type, 'test'):
                errors.append('Pattern failed to compile')
            else:
                # Remove test pattern
                self.pattern_matcher.clear_patterns()

        # Check for Unicode issues
        try:
            pattern.encode('utf-8')
        except UnicodeEncodeError:
            errors.append('Pattern contains invalid Unicode characters')

        return ValidationResult(
            valid=len(errors) == 0,
            pattern=pattern,
            match_type=match_type,
            errors=errors,
            warnings=warnings,
        )

    def validate_patterns_bulk(
        self, patterns: List[Tuple[str, PatternMatchType]]
    ) -> List[ValidationResult]:
        """Validate multiple patterns."""
        results = []
        seen_patterns = set()

        for pattern, match_type in patterns:
            result = self.validate_pattern(pattern, match_type)

            # Check for duplicates
            pattern_key = (pattern.lower(), match_type)
            if pattern_key in seen_patterns:
                result.warnings.append('Duplicate pattern')
            else:
                seen_patterns.add(pattern_key)

            results.append(result)

        return results

    def _check_false_positives(self, pattern: str, match_type: PatternMatchType) -> List[str]:
        """Check for potential false positives."""
        false_positives = []

        for word in self.FALSE_POSITIVE_WORDS:
            test_matcher = PatternMatcher()
            test_matcher.add_pattern(pattern, match_type)

            matches = test_matcher.check_message(word)
            if matches:
                false_positives.append(word)

        return false_positives


class BulkPatternManager:
    """Manages bulk operations for patterns."""

    def __init__(self, db_session: Session):
        self.db = db_session
        self.validator = PatternValidator()

    def parse_input_text(self, text: str) -> List[Tuple[str, PatternMatchType]]:
        """
        Parse input text into patterns.

        Supports:
        - Comma-separated values
        - Newline-separated values
        - Pattern type indicators (word*, *word, *word*)

        Args:
            text: Input text containing patterns

        Returns:
            List of (pattern, match_type) tuples
        """
        patterns = []

        # Split by both commas and newlines
        lines = text.replace(',', '\n').split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Determine match type from pattern format
            if line.startswith('*') and line.endswith('*') and len(line) > 2:
                # Wildcard pattern (*word*)
                pattern = line[1:-1]
                match_type = PatternMatchType.WILDCARD
            elif line.startswith('*'):
                # Suffix pattern (*word)
                pattern = line[1:]
                match_type = PatternMatchType.SUFFIX
            elif line.endswith('*'):
                # Prefix pattern (word*)
                pattern = line[:-1]
                match_type = PatternMatchType.PREFIX
            else:
                # Exact pattern
                pattern = line
                match_type = PatternMatchType.EXACT

            if pattern:
                patterns.append((pattern, match_type))

        return patterns

    def import_patterns_to_rule(
        self,
        rule_id: str,
        patterns: List[Tuple[str, PatternMatchType]],
        validate: bool = True,
        skip_duplicates: bool = True,
    ) -> BulkImportResult:
        """
        Import patterns to a rule.

        Returns:
            BulkImportResult with import statistics
        """
        result = BulkImportResult(
            total_patterns=len(patterns),
            imported_patterns=0,
            skipped_patterns=0,
            errors=[],
            warnings=[],
        )

        # Check if rule exists
        rule = self.db.query(AntiSwearRule).filter(AntiSwearRule.id == rule_id).first()
        if not rule:
            result.errors.append(f'Rule {rule_id} not found')
            return result

        # Get existing patterns if checking for duplicates
        existing_patterns = set()
        if skip_duplicates:
            existing = (
                self.db.query(AntiSwearPattern).filter(AntiSwearPattern.ruleId == rule_id).all()
            )
            existing_patterns = {(p.pattern.lower(), p.matchType) for p in existing}

        # Validate patterns if requested
        if validate:
            validation_results = self.validator.validate_patterns_bulk(patterns)
            for i, validation in enumerate(validation_results):
                if not validation.valid:
                    result.errors.extend(
                        [f'Pattern {i + 1}: {error}' for error in validation.errors]
                    )
                    result.skipped_patterns += 1
                    continue

                if validation.warnings:
                    result.warnings.extend(
                        [f'Pattern {i + 1}: {warning}' for warning in validation.warnings]
                    )

        # Import valid patterns
        for pattern, match_type in patterns:
            pattern = pattern.strip()
            if not pattern:
                result.skipped_patterns += 1
                continue

            # Check for duplicates
            if skip_duplicates and (pattern.lower(), match_type) in existing_patterns:
                result.skipped_patterns += 1
                result.warnings.append(f'Skipped duplicate pattern: {pattern}')
                continue

            try:
                db_pattern = AntiSwearPattern(ruleId=rule_id, pattern=pattern, matchType=match_type)
                self.db.add(db_pattern)
                result.imported_patterns += 1
            except Exception as e:
                result.errors.append(f"Failed to import pattern '{pattern}': {str(e)}")
                result.skipped_patterns += 1

        try:
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            result.errors.append(f'Database error: {str(e)}')
            result.imported_patterns = 0

        return result

    def export_rule_patterns(self, rule_id: str) -> Optional[List[str]]:
        """Export patterns from a rule as formatted strings."""
        patterns = self.db.query(AntiSwearPattern).filter(AntiSwearPattern.ruleId == rule_id).all()

        if not patterns:
            return None

        formatted_patterns = []
        for pattern in patterns:
            if pattern.matchType == PatternMatchType.EXACT:
                formatted_patterns.append(pattern.pattern)
            elif pattern.matchType == PatternMatchType.PREFIX:
                formatted_patterns.append(f'{pattern.pattern}*')
            elif pattern.matchType == PatternMatchType.SUFFIX:
                formatted_patterns.append(f'*{pattern.pattern}')
            elif pattern.matchType == PatternMatchType.WILDCARD:
                formatted_patterns.append(f'*{pattern.pattern}*')

        return formatted_patterns


class ConfigurationManager:
    """Manages import/export of complete filter configurations."""

    def __init__(self, db_session: Session):
        self.db = db_session

    def export_hub_config(self, hub_id: str) -> Optional[Dict[str, Any]]:
        """Export complete filter configuration for a hub."""
        # Get hub info
        hub = self.db.query(Hub).filter(Hub.id == hub_id).first()
        if not hub:
            return None

        # Get rules
        rules = self.db.query(AntiSwearRule).filter(AntiSwearRule.hubId == hub_id).all()

        # Get whitelist
        whitelist = (
            self.db.query(AntiSwearWhitelist).filter(AntiSwearWhitelist.hubId == hub_id).all()
        )

        config = {
            'hub_name': hub.name,
            'hub_id': hub_id,
            'exported_at': datetime.now(timezone.utc).isoformat(),
            'rules': [],
            'whitelist': [w.word for w in whitelist],
        }

        for rule in rules:
            rule_export = RuleExport(
                name=rule.name,
                enabled=rule.enabled,
                actions=[action.value for action in rule.actions],
                warning_threshold=rule.warningThreshold,
                escalation_action=rule.escalationAction.value if rule.escalationAction else None,
                mute_duration_minutes=rule.muteDurationMinutes,
                blacklist_duration_minutes=rule.blacklistDurationMinutes,
                log_violations=rule.logViolations,
                patterns=[
                    {'pattern': p.pattern, 'match_type': p.matchType.value} for p in rule.patterns
                ],
                whitelist=[],  # Rule-specific whitelist if needed
            )
            config['rules'].append(asdict(rule_export))

        return config

    def export_to_csv(self, hub_id: str) -> Optional[str]:
        """Export hub configuration to CSV format."""
        config = self.export_hub_config(hub_id)
        if not config:
            return None

        output = StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow(['Rule Name', 'Pattern', 'Match Type', 'Actions', 'Enabled'])

        # Write patterns
        for rule in config['rules']:
            for pattern in rule['patterns']:
                writer.writerow(
                    [
                        rule['name'],
                        pattern['pattern'],
                        pattern['match_type'],
                        ','.join(rule['actions']),
                        rule['enabled'],
                    ]
                )

        return output.getvalue()

    def copy_config_between_hubs(
        self, source_hub_id: str, target_hub_id: str, created_by: str, overwrite: bool = False
    ) -> bool:
        """
        Copy filter configuration from one hub to another.

        Args:
            source_hub_id: Hub to copy from
            target_hub_id: Hub to copy to
            created_by: User ID creating the copied rules
            overwrite: Whether to overwrite existing rules

        Returns:
            True if copy was successful
        """
        try:
            # Get source configuration
            source_config = self.export_hub_config(source_hub_id)
            if not source_config:
                return False

            # Clear target if overwriting
            if overwrite:
                self.db.query(AntiSwearRule).filter(AntiSwearRule.hubId == target_hub_id).delete()
                self.db.query(AntiSwearWhitelist).filter(
                    AntiSwearWhitelist.hubId == target_hub_id
                ).delete()

            # Copy rules
            for rule_data in source_config['rules']:
                new_rule = AntiSwearRule(
                    hubId=target_hub_id,
                    name=rule_data['name'],
                    createdBy=created_by,
                    enabled=rule_data['enabled'],
                    actions=[BlockWordAction(action) for action in rule_data['actions']],
                    warningThreshold=rule_data['warning_threshold'],
                    escalationAction=BlockWordAction(rule_data['escalation_action'])
                    if rule_data['escalation_action']
                    else None,
                    muteDurationMinutes=rule_data['mute_duration_minutes'],
                    blacklistDurationMinutes=rule_data['blacklist_duration_minutes'],
                    logViolations=rule_data['log_violations'],
                )
                self.db.add(new_rule)
                self.db.flush()  # Get the ID

                # Copy patterns
                for pattern_data in rule_data['patterns']:
                    new_pattern = AntiSwearPattern(
                        ruleId=new_rule.id,
                        pattern=pattern_data['pattern'],
                        matchType=PatternMatchType(pattern_data['match_type']),
                    )
                    self.db.add(new_pattern)

            # Copy whitelist
            for word in source_config['whitelist']:
                whitelist_entry = AntiSwearWhitelist(
                    hubId=target_hub_id,
                    word=word,
                    createdBy=created_by,
                    reason='Copied from another hub',
                )
                self.db.add(whitelist_entry)

            self.db.commit()
            return True
        except Exception:
            self.db.rollback()
            return False
