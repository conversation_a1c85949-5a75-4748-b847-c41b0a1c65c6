"""
Main profanity filter service that orchestrates pattern matching, rule evaluation, and action execution.

This service provides the high-level interface for the profanity filter system.
"""

from datetime import datetime, timedelta, timezone
from typing import TYPE_CHECKING, Dict, List, Optional, Tuple
from dataclasses import dataclass

from sqlalchemy import and_

from utils.modules.core.db.models import (
    AntiSwearRule,
    AntiSwearPattern,
    AntiSwearWhitelist,
    AntiSwearAuditLog,
    PatternMatchType,
    BlockWordAction,
    AlertSeverity,
)
from utils.modules.core.profanity.pattern_matcher import PatternMatcher, MatchResult
from utils.modules.core.profanity.actions import ActionExecutor
from utils.modules.core.profanity.bypass import BypassChecker

if TYPE_CHECKING:
    from sqlalchemy.ext.asyncio import AsyncSession


@dataclass
class FilterResult:
    """Result of profanity filter check."""

    blocked: bool
    matches: List[MatchResult]
    actions_executed: List[BlockWordAction]
    bypass_reason: Optional[str] = None
    severity: AlertSeverity = AlertSeverity.LOW
    rule_id: Optional[str] = None


class ProfanityFilterService:
    def __init__(self, db_session: 'AsyncSession', client_id: str):
        self.db = db_session
        self.pattern_matcher = PatternMatcher()
        self.action_executor = ActionExecutor(db_session, client_id)
        self.bypass_checker = BypassChecker(db_session)

        # Cache for loaded rules per hub
        self._rule_cache: Dict[str, datetime] = {}
        self._cache_ttl = timedelta(minutes=5)  # Cache rules for 5 minutes

    async def check_message(
        self,
        message: str,
        hub_id: str,
        user_id: str,
        channel_id: str,
        message_id: Optional[str] = None,
    ) -> FilterResult:
        """
        Check a message against profanity filters.

        Returns:
            FilterResult with details about any matches and actions taken
        """
        # Load rules for this hub if needed
        await self._ensure_rules_loaded(hub_id)

        # Check for bypass conditions first
        bypass_reason = await self.bypass_checker.check_bypass(hub_id, user_id, message)
        if bypass_reason:
            return FilterResult(
                blocked=False, matches=[], actions_executed=[], bypass_reason=bypass_reason
            )

        # Check message against patterns
        matches = self.pattern_matcher.check_message(message)
        if not matches:
            return FilterResult(blocked=False, matches=[], actions_executed=[])

        # Find the most severe match and corresponding rule
        primary_match, rule = await self._find_primary_match(matches, hub_id)
        if not rule or not rule.enabled:
            return FilterResult(blocked=False, matches=matches, actions_executed=[])

        # Determine severity based on rule and pattern
        severity = self._calculate_severity(primary_match, rule)

        # Execute actions
        actions_executed = await self.action_executor.execute_actions(
            rule.actions,
            hub_id=hub_id,
            user_id=user_id,
            channel_id=channel_id,
            message_id=message_id,
            triggered_word=primary_match.matched_word,
            rule=rule,
        )

        # Log the violation
        await self._log_violation(
            hub_id=hub_id,
            user_id=user_id,
            channel_id=channel_id,
            message_id=message_id,
            message_content=message,
            match=primary_match,
            rule=rule,
            actions_executed=actions_executed,
            severity=severity,
        )

        # Update pattern statistics
        await self._update_pattern_stats(primary_match)

        return FilterResult(
            blocked=BlockWordAction.BLOCK_MESSAGE in actions_executed,
            matches=matches,
            actions_executed=actions_executed,
            severity=severity,
            rule_id=rule.id,
        )

    async def reload_rules(self, hub_id: str) -> bool:
        """
        Force reload of rules for a specific hub.

        Args:
            hub_id: Hub ID to reload rules for

        Returns:
            True if rules were reloaded successfully
        """
        try:
            await self._load_hub_rules(hub_id, force_reload=True)
            return True
        except Exception:
            return False

    async def add_pattern(
        self, hub_id: str, rule_id: str, pattern: str, match_type: PatternMatchType
    ) -> bool:
        """
        Add a new pattern to a rule.

        Args:
            hub_id: Hub ID
            rule_id: Rule ID to add pattern to
            pattern: Pattern text
            match_type: Type of pattern matching

        Returns:
            True if pattern was added successfully
        """
        # Validate the pattern first
        if not self.pattern_matcher.add_pattern(pattern, match_type, 'temp'):
            return False

        # Remove the temporary pattern
        self.pattern_matcher.clear_patterns()

        # Add to database
        try:
            db_pattern = AntiSwearPattern(ruleId=rule_id, pattern=pattern, matchType=match_type)
            self.db.add(db_pattern)
            self.db.commit()

            # Reload rules for this hub
            await self.reload_rules(hub_id)
            return True
        except Exception:
            self.db.rollback()
            return False

    async def _ensure_rules_loaded(self, hub_id: str) -> None:
        """Ensure rules are loaded and up-to-date for a hub."""
        now = datetime.now(timezone.utc)
        last_loaded = self._rule_cache.get(hub_id)

        if last_loaded is None or (now - last_loaded) > self._cache_ttl:
            await self._load_hub_rules(hub_id)

    async def _load_hub_rules(self, hub_id: str, force_reload: bool = False) -> None:
        """Load all rules and patterns for a hub."""
        # Clear existing patterns for this hub if force reloading
        if force_reload:
            self.pattern_matcher.clear_patterns()

        # Load active rules
        rules = (
            self.db.query(AntiSwearRule)
            .filter(and_(AntiSwearRule.hubId == hub_id, AntiSwearRule.enabled == True))
            .all()
        )

        # Load patterns for each rule
        for rule in rules:
            for pattern in rule.patterns:
                self.pattern_matcher.add_pattern(pattern.pattern, pattern.matchType, pattern.id)

        # Load whitelist
        whitelist_words = (
            self.db.query(AntiSwearWhitelist).filter(AntiSwearWhitelist.hubId == hub_id).all()
        )

        for whitelist_word in whitelist_words:
            self.pattern_matcher.add_whitelist_word(whitelist_word.word)

        # Update cache
        self._rule_cache[hub_id] = datetime.now()

    async def _find_primary_match(
        self, matches: List[MatchResult], hub_id: str
    ) -> Tuple[Optional[MatchResult], Optional[AntiSwearRule]]:
        """Find the most severe match and its corresponding rule."""
        if not matches:
            return None, None

        # Get all pattern IDs from matches
        pattern_ids = [match.pattern_id for match in matches if match.pattern_id]
        if not pattern_ids:
            return matches[0], None

        # Find patterns and their rules
        patterns = (
            self.db.query(AntiSwearPattern).filter(AntiSwearPattern.id.in_(pattern_ids)).all()
        )

        pattern_to_rule = {}
        for pattern in patterns:
            rule = self.db.query(AntiSwearRule).filter(AntiSwearRule.id == pattern.ruleId).first()
            if rule:
                pattern_to_rule[pattern.id] = rule

        # Find the match with the highest priority rule
        # Priority: rules with more severe actions first
        best_match = None
        best_rule = None
        best_priority = -1

        for match in matches:
            if match.pattern_id in pattern_to_rule:
                rule = pattern_to_rule[match.pattern_id]
                priority = self._calculate_rule_priority(rule)
                if priority > best_priority:
                    best_match = match
                    best_rule = rule
                    best_priority = priority

        return best_match or matches[0], best_rule

    def _calculate_rule_priority(self, rule: AntiSwearRule) -> int:
        """Calculate priority score for a rule based on its actions."""
        priority = 0
        action_weights = {
            BlockWordAction.BLOCK_MESSAGE: 1,
            BlockWordAction.WARN: 2,
            BlockWordAction.SEND_ALERT: 3,
            BlockWordAction.MUTE: 4,
            BlockWordAction.BAN: 5,
            BlockWordAction.BLACKLIST: 5,
        }

        for action in rule.actions:
            priority += action_weights.get(action, 0)

        return priority

    def _calculate_severity(self, match: MatchResult, rule: AntiSwearRule) -> AlertSeverity:
        """Calculate severity based on match and rule."""
        # Base severity on actions
        if BlockWordAction.BAN in rule.actions or BlockWordAction.BLACKLIST in rule.actions:
            return AlertSeverity.CRITICAL
        elif BlockWordAction.MUTE in rule.actions:
            return AlertSeverity.HIGH
        elif BlockWordAction.WARN in rule.actions:
            return AlertSeverity.MEDIUM
        else:
            return AlertSeverity.LOW

    async def _log_violation(
        self,
        hub_id: str,
        user_id: str,
        channel_id: str,
        message_id: Optional[str],
        message_content: str,
        match: MatchResult,
        rule: AntiSwearRule,
        actions_executed: List[BlockWordAction],
        severity: AlertSeverity,
    ) -> None:
        """Log a profanity filter violation."""
        if not rule.logViolations:
            return

        audit_log = AntiSwearAuditLog(
            hubId=hub_id,
            ruleId=rule.id,
            patternId=match.pattern_id,
            userId=user_id,
            messageId=message_id,
            channelId=channel_id,
            triggeredWord=match.matched_word,
            originalMessage=message_content,
            actionsExecuted=actions_executed,
            severity=severity,
        )

        self.db.add(audit_log)
        self.db.commit()

    async def _update_pattern_stats(self, match: MatchResult) -> None:
        """Update statistics for a triggered pattern."""
        if not match.pattern_id:
            return

        pattern = (
            self.db.query(AntiSwearPattern).filter(AntiSwearPattern.id == match.pattern_id).first()
        )

        if pattern:
            pattern.triggerCount += 1
            pattern.lastTriggered = datetime.now(timezone.utc)
            self.db.commit()
