"""
Analytics and reporting system for profanity filter violations and effectiveness.

Provides comprehensive analytics including:
- Violation statistics and trends
- Pattern effectiveness tracking
- False positive detection
- Performance metrics
- Audit trail analysis
"""

from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from collections import defaultdict

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc

from utils.modules.core.db.models import (
    AntiSwearAuditLog,
    AntiSwearRule,
    AntiSwearPattern,
    AntiSwearWhitelist,
    BlockWordAction,
    AlertSeverity,
    PatternMatchType,
)


@dataclass
class ViolationStats:
    """Statistics for profanity violations."""

    total_violations: int
    blocked_messages: int
    warnings_issued: int
    mutes_issued: int
    bans_issued: int
    alerts_sent: int
    unique_users: int
    unique_patterns: int
    severity_breakdown: Dict[str, int]
    top_triggered_words: List[Tuple[str, int]]
    hourly_distribution: Dict[int, int]


@dataclass
class PatternEffectiveness:
    """Effectiveness metrics for patterns."""

    pattern_id: str
    pattern: str
    match_type: str
    trigger_count: int
    last_triggered: Optional[datetime]
    false_positive_rate: float
    effectiveness_score: float


@dataclass
class TrendData:
    """Trend data over time."""

    period: str
    violations: int
    unique_users: int
    blocked_messages: int
    timestamp: datetime


class ProfanityAnalytics:
    """
    Analytics engine for profanity filter system.

    Provides insights into filter effectiveness, violation patterns,
    and system performance.
    """

    def __init__(self, db_session: Session):
        self.db = db_session

    def get_violation_stats(
        self,
        hub_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
    ) -> ViolationStats:
        """
        Get comprehensive violation statistics for a hub.

        Args:
            hub_id: Hub ID to analyze
            start_date: Start of analysis period
            end_date: End of analysis period

        Returns:
            ViolationStats with comprehensive metrics
        """
        if start_date is None:
            start_date = datetime.now(timezone.utc) - timedelta(days=30)
        if end_date is None:
            end_date = datetime.now(timezone.utc)

        # Base query
        base_query = self.db.query(AntiSwearAuditLog).filter(
            and_(
                AntiSwearAuditLog.hubId == hub_id,
                AntiSwearAuditLog.createdAt >= start_date,
                AntiSwearAuditLog.createdAt <= end_date,
            )
        )

        # Total violations
        total_violations = base_query.count()

        # Action counts
        blocked_messages = base_query.filter(
            AntiSwearAuditLog.actionsExecuted.contains([BlockWordAction.BLOCK_MESSAGE])
        ).count()

        warnings_issued = base_query.filter(
            AntiSwearAuditLog.actionsExecuted.contains([BlockWordAction.WARN])
        ).count()

        mutes_issued = base_query.filter(
            AntiSwearAuditLog.actionsExecuted.contains([BlockWordAction.MUTE])
        ).count()

        bans_issued = base_query.filter(
            or_(
                AntiSwearAuditLog.actionsExecuted.contains([BlockWordAction.BAN]),
                AntiSwearAuditLog.actionsExecuted.contains([BlockWordAction.BLACKLIST]),
            )
        ).count()

        alerts_sent = base_query.filter(
            AntiSwearAuditLog.actionsExecuted.contains([BlockWordAction.SEND_ALERT])
        ).count()

        # Unique counts
        unique_users = (
            self.db.query(func.count(func.distinct(AntiSwearAuditLog.userId)))
            .filter(
                and_(
                    AntiSwearAuditLog.hubId == hub_id,
                    AntiSwearAuditLog.createdAt >= start_date,
                    AntiSwearAuditLog.createdAt <= end_date,
                )
            )
            .scalar()
            or 0
        )

        unique_patterns = (
            self.db.query(func.count(func.distinct(AntiSwearAuditLog.patternId)))
            .filter(
                and_(
                    AntiSwearAuditLog.hubId == hub_id,
                    AntiSwearAuditLog.createdAt >= start_date,
                    AntiSwearAuditLog.createdAt <= end_date,
                    AntiSwearAuditLog.patternId.isnot(None),
                )
            )
            .scalar()
            or 0
        )

        # Severity breakdown
        severity_breakdown = {}
        for severity in AlertSeverity:
            count = base_query.filter(AntiSwearAuditLog.severity == severity).count()
            severity_breakdown[severity.value] = count

        # Top triggered words
        top_words_query = (
            self.db.query(
                AntiSwearAuditLog.triggeredWord,
                func.count(AntiSwearAuditLog.triggeredWord).label('count'),
            )
            .filter(
                and_(
                    AntiSwearAuditLog.hubId == hub_id,
                    AntiSwearAuditLog.createdAt >= start_date,
                    AntiSwearAuditLog.createdAt <= end_date,
                )
            )
            .group_by(AntiSwearAuditLog.triggeredWord)
            .order_by(desc('count'))
            .limit(10)
        )

        top_triggered_words = [(word, count) for word, count in top_words_query.all()]

        # Hourly distribution
        hourly_distribution = defaultdict(int)
        hourly_query = (
            self.db.query(
                func.extract('hour', AntiSwearAuditLog.createdAt).label('hour'),
                func.count(AntiSwearAuditLog.id).label('count'),
            )
            .filter(
                and_(
                    AntiSwearAuditLog.hubId == hub_id,
                    AntiSwearAuditLog.createdAt >= start_date,
                    AntiSwearAuditLog.createdAt <= end_date,
                )
            )
            .group_by('hour')
            .all()
        )

        for hour, count in hourly_query:
            hourly_distribution[int(hour)] = count

        return ViolationStats(
            total_violations=total_violations,
            blocked_messages=blocked_messages,
            warnings_issued=warnings_issued,
            mutes_issued=mutes_issued,
            bans_issued=bans_issued,
            alerts_sent=alerts_sent,
            unique_users=unique_users,
            unique_patterns=unique_patterns,
            severity_breakdown=severity_breakdown,
            top_triggered_words=top_triggered_words,
            hourly_distribution=dict(hourly_distribution),
        )

    def get_pattern_effectiveness(self, hub_id: str, days: int = 30) -> List[PatternEffectiveness]:
        """
        Analyze pattern effectiveness and identify potential issues.

        Args:
            hub_id: Hub ID to analyze
            days: Number of days to analyze

        Returns:
            List of PatternEffectiveness metrics
        """
        since_date = datetime.now(timezone.utc) - timedelta(days=days)

        # Get all patterns for the hub
        patterns_query = (
            self.db.query(AntiSwearPattern)
            .join(AntiSwearRule)
            .filter(AntiSwearRule.hubId == hub_id)
        )

        effectiveness_data = []

        for pattern in patterns_query.all():
            # Get trigger count from audit logs
            trigger_count = (
                self.db.query(AntiSwearAuditLog)
                .filter(
                    and_(
                        AntiSwearAuditLog.patternId == pattern.id,
                        AntiSwearAuditLog.createdAt >= since_date,
                    )
                )
                .count()
            )

            # Calculate false positive rate (simplified)
            # In a real implementation, this would require manual review data
            false_positive_rate = self._estimate_false_positive_rate(pattern, trigger_count)

            # Calculate effectiveness score
            effectiveness_score = self._calculate_effectiveness_score(
                pattern, trigger_count, false_positive_rate
            )

            effectiveness_data.append(
                PatternEffectiveness(
                    pattern_id=pattern.id,
                    pattern=pattern.pattern,
                    match_type=pattern.matchType.value,
                    trigger_count=trigger_count,
                    last_triggered=pattern.lastTriggered,
                    false_positive_rate=false_positive_rate,
                    effectiveness_score=effectiveness_score,
                )
            )

        # Sort by effectiveness score (descending)
        effectiveness_data.sort(key=lambda x: x.effectiveness_score, reverse=True)

        return effectiveness_data

    def get_trend_data(
        self, hub_id: str, days: int = 30, granularity: str = 'daily'
    ) -> List[TrendData]:
        """
        Get trend data for violations over time.

        Args:
            hub_id: Hub ID to analyze
            days: Number of days to analyze
            granularity: 'hourly', 'daily', or 'weekly'

        Returns:
            List of TrendData points
        """
        since_date = datetime.now(timezone.utc) - timedelta(days=days)

        if granularity == 'hourly':
            date_trunc = func.date_trunc('hour', AntiSwearAuditLog.createdAt)
            period_format = '%Y-%m-%d %H:00'
        elif granularity == 'weekly':
            date_trunc = func.date_trunc('week', AntiSwearAuditLog.createdAt)
            period_format = '%Y-W%U'
        else:  # daily
            date_trunc = func.date_trunc('day', AntiSwearAuditLog.createdAt)
            period_format = '%Y-%m-%d'

        trend_query = (
            self.db.query(
                date_trunc.label('period'),
                func.count(AntiSwearAuditLog.id).label('violations'),
                func.count(func.distinct(AntiSwearAuditLog.userId)).label('unique_users'),
                func.sum(
                    func.case(
                        [
                            (
                                AntiSwearAuditLog.actionsExecuted.contains(
                                    [BlockWordAction.BLOCK_MESSAGE]
                                ),
                                1,
                            )
                        ],
                        else_=0,
                    )
                ).label('blocked_messages'),
            )
            .filter(
                and_(AntiSwearAuditLog.hubId == hub_id, AntiSwearAuditLog.createdAt >= since_date)
            )
            .group_by('period')
            .order_by('period')
        )

        trend_data = []
        for period, violations, unique_users, blocked_messages in trend_query.all():
            trend_data.append(
                TrendData(
                    period=period.strftime(period_format),
                    violations=violations or 0,
                    unique_users=unique_users or 0,
                    blocked_messages=blocked_messages or 0,
                    timestamp=period,
                )
            )

        return trend_data

    def get_user_violation_summary(
        self, hub_id: str, user_id: str, days: int = 30
    ) -> Dict[str, Any]:
        """Get violation summary for a specific user."""
        since_date = datetime.now(timezone.utc) - timedelta(days=days)

        violations = (
            self.db.query(AntiSwearAuditLog)
            .filter(
                and_(
                    AntiSwearAuditLog.hubId == hub_id,
                    AntiSwearAuditLog.userId == user_id,
                    AntiSwearAuditLog.createdAt >= since_date,
                )
            )
            .all()
        )

        if not violations:
            return {
                'total_violations': 0,
                'first_violation': None,
                'last_violation': None,
                'most_common_words': [],
                'severity_breakdown': {},
                'actions_received': {},
            }

        # Analyze violations
        words = [v.triggeredWord for v in violations]
        word_counts = defaultdict(int)
        for word in words:
            word_counts[word] += 1

        severity_counts = defaultdict(int)
        action_counts = defaultdict(int)

        for violation in violations:
            severity_counts[violation.severity.value] += 1
            for action in violation.actionsExecuted:
                action_counts[action.value] += 1

        return {
            'total_violations': len(violations),
            'first_violation': min(v.createdAt for v in violations),
            'last_violation': max(v.createdAt for v in violations),
            'most_common_words': sorted(word_counts.items(), key=lambda x: x[1], reverse=True)[:5],
            'severity_breakdown': dict(severity_counts),
            'actions_received': dict(action_counts),
        }

    def _estimate_false_positive_rate(self, pattern: AntiSwearPattern, trigger_count: int) -> float:
        """
        Estimate false positive rate for a pattern.

        This is a simplified estimation. In a real implementation,
        this would use manual review data or machine learning.
        """
        if trigger_count == 0:
            return 0.0

        # Base false positive rate by pattern type
        base_rates = {
            PatternMatchType.EXACT: 0.05,
            PatternMatchType.PREFIX: 0.15,
            PatternMatchType.SUFFIX: 0.20,
            PatternMatchType.WILDCARD: 0.30,
        }

        base_rate = base_rates.get(pattern.matchType, 0.10)

        # Adjust based on pattern length (shorter = higher false positive rate)
        if len(pattern.pattern) <= 3:
            base_rate *= 2.0
        elif len(pattern.pattern) <= 5:
            base_rate *= 1.5

        # Cap at 100%
        return min(base_rate, 1.0)

    def _calculate_effectiveness_score(
        self, pattern: AntiSwearPattern, trigger_count: int, false_positive_rate: float
    ) -> float:
        """
        Calculate an effectiveness score for a pattern.

        Higher scores indicate more effective patterns.
        """
        if trigger_count == 0:
            return 0.0

        # Base score from trigger count (logarithmic to prevent dominance)
        import math

        base_score = math.log10(trigger_count + 1) * 10

        # Penalty for false positives
        false_positive_penalty = false_positive_rate * 50

        # Bonus for exact matches (more precise)
        precision_bonus = 10 if pattern.matchType == PatternMatchType.EXACT else 0

        # Calculate final score
        score = base_score - false_positive_penalty + precision_bonus

        return max(score, 0.0)

    def generate_report(self, hub_id: str, days: int = 30) -> Dict[str, Any]:
        """Generate a comprehensive analytics report."""
        stats = self.get_violation_stats(hub_id, datetime.now(timezone.utc) - timedelta(days=days))
        effectiveness = self.get_pattern_effectiveness(hub_id, days)
        trends = self.get_trend_data(hub_id, days)

        # Calculate additional metrics
        avg_violations_per_day = stats.total_violations / days if days > 0 else 0
        violation_rate = stats.total_violations / max(stats.unique_users, 1)

        return {
            'hub_id': hub_id,
            'analysis_period_days': days,
            'generated_at': datetime.now(timezone.utc).isoformat(),
            'summary': {
                'total_violations': stats.total_violations,
                'avg_violations_per_day': round(avg_violations_per_day, 2),
                'unique_users_affected': stats.unique_users,
                'violation_rate_per_user': round(violation_rate, 2),
                'most_effective_patterns': effectiveness[:5],
                'least_effective_patterns': effectiveness[-5:] if len(effectiveness) > 5 else [],
            },
            'detailed_stats': stats,
            'pattern_effectiveness': effectiveness,
            'trends': trends,
        }
