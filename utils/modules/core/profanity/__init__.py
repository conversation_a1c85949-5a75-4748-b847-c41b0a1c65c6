"""
Comprehensive profanity filter system for InterChat.

This module provides a complete profanity filtering solution with:
- Pattern matching engine with exact, prefix, suffix, and wildcard patterns
- Moderation actions (block, warn, mute, ban, alert)
- Bypass system with role-based exemptions and temporary tokens
- Comprehensive audit logging and analytics
- Per-hub customization and whitelist support

Main Components:
- PatternMatcher: Core pattern matching with word boundaries
- ProfanityFilterService: Main orchestration service
- ActionExecutor: Handles moderation actions and escalation
- BypassChecker: Manages exemptions and bypass tokens

Usage:
    from utils.modules.core.profanity import ProfanityFilterService

    # Initialize with database session
    filter_service = ProfanityFilterService(db_session)

    # Check a message
    result = await filter_service.check_message(
        message="Some message content",
        hub_id="hub_123",
        user_id="user_456",
        channel_id="channel_789"
    )

    if result.blocked:
        # Message was blocked
        print(f"Message blocked due to: {result.matches}")
        print(f"Actions taken: {result.actions_executed}")
"""

from .pattern_matcher import <PERSON><PERSON><PERSON><PERSON><PERSON>, MatchResult
from .filter_service import ProfanityFilterService, FilterResult
from .actions import ActionExecutor, ActionContext
from .bypass import Bypass<PERSON>he<PERSON>, BypassConfig

__all__ = [
    'PatternMatcher',
    'MatchResult',
    'ProfanityFilterService',
    'FilterResult',
    'ActionExecutor',
    'ActionContext',
    'BypassChecker',
    'BypassConfig',
]
