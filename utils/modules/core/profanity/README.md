# Profanity Filter System

A comprehensive, high-performance profanity filtering system for InterChat with advanced pattern matching, flexible moderation actions, and robust bypass mechanisms.

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    ProfanityFilterService                   │
│                   (Main Orchestrator)                      │
└─────────────────┬───────────────────┬───────────────────────┘
                  │                   │
         ┌────────▼────────┐ ┌────────▼────────┐
         │ PatternMatcher  │ │  Bypass<PERSON>he<PERSON>  │
         │   (Core Engine) │ │   (Exemptions)  │
         └─────────────────┘ └─────────────────┘
                  │                   │
         ┌────────▼────────┐ ┌────────▼────────┐
         │ ActionExecutor  │ │   Analytics     │
         │  (Moderation)   │ │  (Reporting)    │
         └─────────────────┘ └─────────────────┘
                  │
         ┌────────▼────────┐
         │   AdminTools    │
         │ (Management)    │
         └─────────────────┘
```

## Core Components

### PatternMatcher (`pattern_matcher.py`)
- High-performance regex-based pattern matching
- Support for exact, prefix, suffix, and wildcard patterns
- Word boundary detection to prevent false positives
- Unicode normalization and bypass character handling
- Whitelist support for safe words

### ProfanityFilterService (`filter_service.py`)
- Main service orchestrating all components
- Rule caching and management
- Message processing pipeline
- Integration with database models
- Async/await support for high performance

### ActionExecutor (`actions.py`)
- Handles all moderation actions (block, warn, mute, ban, alert)
- Escalation system with configurable thresholds
- Duration management for temporary actions
- Integration with existing infraction system

### BypassChecker (`bypass.py`)
- Role-based exemptions (owners, moderators, managers)
- Discord role exemptions
- Temporary bypass tokens with expiration
- Comprehensive bypass logging

### AdminTools (`admin_tools.py`)
- Bulk pattern import/export
- Pattern validation and testing
- Configuration management
- Hub-to-hub configuration copying
- CSV export functionality

### Analytics (`analytics.py`)
- Violation statistics and trends
- Pattern effectiveness tracking
- False positive detection
- Performance metrics
- Comprehensive reporting

## Database Schema

### New Tables
- `AntiSwearWhitelist`: Per-hub safe words
- `AntiSwearAuditLog`: Comprehensive violation logging
- `AntiSwearBypassToken`: Temporary bypass tokens

### Enhanced Tables
- `AntiSwearRule`: Added `enabled`, warning thresholds, escalation
- `AntiSwearPattern`: Added `matchType`, statistics tracking

### New Enums
- `PatternMatchType`: EXACT, PREFIX, SUFFIX, WILDCARD
- `AlertSeverity`: LOW, MEDIUM, HIGH, CRITICAL

## Usage Examples

### Basic Message Filtering
```python
from utils.modules.core.profanity import ProfanityFilterService

# Initialize service
filter_service = ProfanityFilterService(db_session)

# Check message
result = await filter_service.check_message(
    message="User message content",
    hub_id="hub_123",
    user_id="user_456",
    channel_id="channel_789"
)

if result.blocked:
    # Handle blocked message
    print(f"Blocked: {result.matches}")
    print(f"Actions: {result.actions_executed}")
```

### Pattern Management
```python
from utils.modules.core.profanity.admin_tools import BulkPatternManager

manager = BulkPatternManager(db_session)

# Bulk import patterns
patterns = manager.parse_input_text("word1, word2*, *word3, *word4*")
result = manager.import_patterns_to_rule("rule_id", patterns)
```

### Analytics
```python
from utils.modules.core.profanity.analytics import ProfanityAnalytics

analytics = ProfanityAnalytics(db_session)
stats = analytics.get_violation_stats("hub_123")
report = analytics.generate_report("hub_123", days=30)
```

## Performance Features

### Optimizations
- **Precompiled Patterns**: All regex patterns compiled once at load
- **Rule Caching**: Hub rules cached for 5 minutes
- **Efficient Queries**: Optimized database indexes
- **Batch Operations**: Bulk pattern operations
- **Lazy Loading**: Relationships loaded only when needed

### Benchmarks
- **Pattern Compilation**: 1000 patterns in <2 seconds
- **Message Checking**: 100 checks of 10k-word message in <1 second
- **Database Operations**: Optimized for high-throughput scenarios

## Configuration

### Environment Variables
```bash
# Optional: Configure cache TTL (default: 5 minutes)
PROFANITY_CACHE_TTL_MINUTES=5

# Optional: Enable debug logging
PROFANITY_DEBUG=true
```

### Database Migration
```sql
-- Run database migrations to add new tables and columns
-- See migration files in database schema updates
```

## Testing

### Run Tests
```bash
# Run all profanity filter tests
pytest tests/test_profanity_filter.py -v

# Run specific test categories
pytest tests/test_profanity_filter.py::TestPatternMatcher -v
pytest tests/test_profanity_filter.py::TestPerformance -v
```

### Test Coverage
- Pattern matching: All types and edge cases
- Action execution: All actions and escalation
- Bypass mechanisms: All bypass types
- Performance: Large-scale scenarios
- Integration: End-to-end workflows

## Security Considerations

### Input Validation
- All patterns validated before compilation
- SQL injection prevention through ORM
- XSS prevention in admin interfaces
- Rate limiting for API endpoints

### Access Control
- Role-based access to admin functions
- Audit logging for all configuration changes
- Secure token generation for bypass tokens
- Permission checks for sensitive operations

## Monitoring

### Key Metrics
- Pattern match rates
- False positive rates
- Action execution success rates
- Performance metrics (latency, throughput)
- Cache hit rates

### Alerts
- High false positive rates
- Performance degradation
- Failed action executions
- Unusual bypass usage

## Troubleshooting

### Common Issues
1. **Patterns not matching**: Check pattern type and word boundaries
2. **False positives**: Use whitelist or refine patterns
3. **Performance issues**: Review pattern complexity and caching
4. **Actions not executing**: Check user permissions and rule configuration

### Debug Tools
```python
# Enable debug logging
import logging
logging.getLogger('profanity_filter').setLevel(logging.DEBUG)

# Test pattern matching
from utils.modules.core.profanity import PatternMatcher
matcher = PatternMatcher()
matcher.add_pattern("test", PatternMatchType.EXACT)
matches = matcher.check_message("test message")
```

## Contributing

### Development Setup
1. Install dependencies: `pip install -r requirements.txt`
2. Run tests: `pytest tests/test_profanity_filter.py`
3. Check code style: `black utils/modules/core/profanity/`
4. Type checking: `mypy utils/modules/core/profanity/`

### Guidelines
- Follow existing code patterns
- Add comprehensive tests for new features
- Update documentation for API changes
- Consider performance implications
- Maintain backward compatibility

## License

This profanity filter system is part of the InterChat project and follows the same licensing terms.
