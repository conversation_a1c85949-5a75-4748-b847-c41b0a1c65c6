"""
Action execution system for profanity filter violations.

Handles different moderation actions like blocking, warning, muting, banning, and alerting.
"""

import asyncio
from datetime import datetime, timedelta, timezone
from typing import TYPE_CHECKING, List, Optional, Dict, Any
from dataclasses import dataclass

from sqlalchemy.orm import Session
from sqlalchemy import and_, func, or_, select

from utils.modules.core.db.models import (
    BlockWordAction,
    AntiSwearRule,
    Infraction,
    InfractionType,
    InfractionStatus,
    Hub,
    User,
    HubLogConfig,
)
from utils.modules.services.moderationService import ModerationService

if TYPE_CHECKING:
    from sqlalchemy.ext.asyncio import AsyncSession


@dataclass
class ActionContext:
    """Context information for executing actions."""

    hub_id: str
    user_id: str
    channel_id: str
    message_id: Optional[str]
    triggered_word: str
    rule: AntiSwearRule


class ActionExecutor:
    """
    Executes moderation actions for profanity filter violations.

    Handles escalation, duration management, and logging.
    """

    def __init__(self, db_session: 'AsyncSession', client_id: str):
        self.db = db_session
        self.modsvc = ModerationService(self.db)
        self.client_id = client_id

    async def execute_actions(
        self,
        actions: List[BlockWordAction],
        hub_id: str,
        user_id: str,
        channel_id: str,
        message_id: Optional[str],
        triggered_word: str,
        rule: AntiSwearRule,
    ) -> List[BlockWordAction]:
        """
        Execute a list of actions for a profanity violation.

        Returns:
            List of actions that were successfully executed
        """
        context = ActionContext(
            hub_id=hub_id,
            user_id=user_id,
            channel_id=channel_id,
            message_id=message_id,
            triggered_word=triggered_word,
            rule=rule,
        )

        executed_actions = []

        for action in actions:
            try:
                success = await self._execute_single_action(action, context)
                if success:
                    executed_actions.append(action)
            except Exception as e:
                # Log error but continue with other actions
                print(f'Error executing action {action}: {e}')
                continue

        return executed_actions

    async def _execute_single_action(self, action: BlockWordAction, context: ActionContext) -> bool:
        """Execute a single action."""
        if action == BlockWordAction.BLOCK_MESSAGE:
            return await self._block_message(context)
        elif action == BlockWordAction.WARN:
            await self._warn_user(context)
            return True
        elif action == BlockWordAction.MUTE:
            return await self._mute_user(context)
        elif action == BlockWordAction.BAN or action == BlockWordAction.BLACKLIST:
            return await self._ban_user(context)
        elif action == BlockWordAction.SEND_ALERT:
            return await self._send_alert(context)
        else:
            return False

    async def _block_message(self, context: ActionContext) -> bool:
        """
        Block/delete the message.

        Note: This is handled at the application level by returning blocked=True
        in the FilterResult. The actual message deletion would be handled by
        the Discord bot or web interface.
        """
        return True

    async def _warn_user(self, context: ActionContext) -> None:
        """Issue a warning to the user."""
        # Check if user has reached warning threshold
        existing_warnings = (
            await self.db.execute(
                select(func.count())
                .select_from(Infraction)
                .where(
                    and_(
                        Infraction.hubId == context.hub_id,
                        Infraction.userId == context.user_id,
                        Infraction.type == InfractionType.WARNING,
                        Infraction.status == InfractionStatus.ACTIVE,
                    )
                )
            )
        ).scalar() or 0

        # Create warning infraction
        await self.modsvc.create_infraction(
            hub_id=context.hub_id,
            user_id=context.user_id,
            mod_id=self.client_id,
            infraction_type=InfractionType.WARNING,
            reason=f"Profanity filter violation: '{context.triggered_word}' (Rule: {context.rule.name})",
            server_id=None,
            server_name=None,
        )

        # Check if escalation is needed
        if (
            existing_warnings + 1
        ) >= context.rule.warningThreshold and context.rule.escalationAction:
            await self._execute_single_action(context.rule.escalationAction, context)

    async def _mute_user(self, context: ActionContext) -> bool:
        """Mute the user for a specified duration."""
        try:
            # Calculate expiry time
            duration_minutes = context.rule.muteDurationMinutes or 60  # Default 1 hour
            expires_at = datetime.now(timezone.utc) + timedelta(minutes=duration_minutes)

            # Check for existing active mute
            stmt = select(Infraction).where(
                and_(
                    Infraction.hubId == context.hub_id,
                    Infraction.userId == context.user_id,
                    Infraction.type == InfractionType.MUTE,
                    Infraction.status == InfractionStatus.ACTIVE,
                    or_(
                        Infraction.expiresAt.is_(None),
                        Infraction.expiresAt > datetime.now(timezone.utc),
                    ),
                )
            )
            existing_mute = await self.db.scalar(stmt)

            if existing_mute:
                # Extend existing mute
                if existing_mute.expiresAt:
                    existing_mute.expiresAt = max(existing_mute.expiresAt, expires_at)
                else:
                    existing_mute.expiresAt = expires_at
            else:
                # Create new mute
                await self.modsvc.create_infraction(
                    hub_id=context.hub_id,
                    user_id=context.user_id,
                    mod_id=self.client_id,
                    infraction_type=InfractionType.MUTE,
                    reason=f"Profanity filter violation: '{context.triggered_word}' (Rule: {context.rule.name})",
                    duration_ms=duration_minutes * 60 * 1000,
                )
            return True
        except Exception:
            return False

    async def _ban_user(self, context: ActionContext) -> bool:
        """Ban the user from the hub."""
        try:
            # Calculate expiry time if temporary
            expires_at = None
            if context.rule.blacklistDurationMinutes:
                expires_at = datetime.now(timezone.utc) + timedelta(
                    minutes=context.rule.blacklistDurationMinutes
                )

            # Check for existing active ban
            stmt = select(Infraction).where(
                and_(
                    Infraction.hubId == context.hub_id,
                    Infraction.userId == context.user_id,
                    Infraction.type == InfractionType.BAN,
                    Infraction.status == InfractionStatus.ACTIVE,
                )
            )
            existing_ban = await self.db.scalar(stmt)

            if existing_ban:
                # Extend existing ban if new one is longer or permanent
                if expires_at is None or (
                    existing_ban.expiresAt and expires_at > existing_ban.expiresAt
                ):
                    existing_ban.expiresAt = expires_at
                    existing_ban.reason += f" | Additional violation: '{context.triggered_word}'"
            else:
                # Create new ban
                await self.modsvc.create_infraction(
                    hub_id=context.hub_id,
                    user_id=context.user_id,
                    mod_id=self.client_id,
                    infraction_type=InfractionType.BAN,
                    reason=f"Profanity filter violation: '{context.triggered_word}' (Rule: {context.rule.name})",
                    duration_ms=(context.rule.blacklistDurationMinutes or 5) * 60 * 1000,
                )
            return True
        except Exception:
            return False

    async def _send_alert(self, context: ActionContext) -> bool:
        """
        Send an alert to moderators.

        This would typically integrate with the hub's logging configuration
        to send alerts to the appropriate channels.
        """
        try:
            # Get hub log configuration
            log_config = (
                self.db.query(HubLogConfig).filter(HubLogConfig.hubId == context.hub_id).first()
            )

            if not log_config or not log_config.messageModerationChannelId:
                # No moderation channel configured
                return False

            # Create alert data
            alert_data = {
                'type': 'profanity_violation',
                'hub_id': context.hub_id,
                'user_id': context.user_id,
                'channel_id': context.channel_id,
                'message_id': context.message_id,
                'triggered_word': context.triggered_word,
                'rule_name': context.rule.name,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'moderation_channel': log_config.messageModerationChannelId,
                'moderation_role': log_config.messageModerationRoleId,
            }

            # In a real implementation, this would send the alert to Discord
            # For now, we'll just mark it as successful
            # TODO: Integrate with Discord webhook or bot API

            return True
        except Exception:
            return False

    async def get_user_violation_count(self, hub_id: str, user_id: str, days: int = 30) -> int:
        """Get the number of violations for a user in the specified time period."""
        since_date = datetime.now(timezone.utc) - timedelta(days=days)

        count = (
            self.db.query(Infraction)
            .filter(
                and_(
                    Infraction.hubId == hub_id,
                    Infraction.userId == user_id,
                    Infraction.createdAt >= since_date,
                    Infraction.status == InfractionStatus.ACTIVE,
                )
            )
            .count()
        )

        return count

    async def get_active_infractions(self, hub_id: str, user_id: str) -> List[Infraction]:
        """Get all active infractions for a user in a hub."""
        infractions = (
            self.db.query(Infraction)
            .filter(
                and_(
                    Infraction.hubId == hub_id,
                    Infraction.userId == user_id,
                    Infraction.status == InfractionStatus.ACTIVE,
                    or_(
                        Infraction.expiresAt.is_(None),
                        Infraction.expiresAt > datetime.now(timezone.utc),
                    ),
                )
            )
            .all()
        )

        return infractions

    async def revoke_infraction(self, infraction_id: str, moderator_id: str, reason: str) -> bool:
        """Revoke an active infraction."""
        try:
            infraction = self.db.query(Infraction).filter(Infraction.id == infraction_id).first()

            if not infraction:
                return False

            infraction.status = InfractionStatus.REVOKED
            infraction.reason += f' | Revoked by {moderator_id}: {reason}'

            self.db.commit()
            return True
        except Exception:
            self.db.rollback()
            return False
