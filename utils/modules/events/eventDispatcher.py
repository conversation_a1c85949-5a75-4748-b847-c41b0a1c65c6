"""
Custom Event Dispatcher for InterChat

A global, reusable event system that allows decoupled communication between
different parts of the application. Components can dispatch events and
register listeners without direct dependencies.
"""

import asyncio
from typing import Any, Callable, Dict, List, Optional, Union, Awaitable
from dataclasses import dataclass
from datetime import datetime, timezone
from enum import Enum

from utils.constants import logger

# Type aliases for better readability
EventCallback = Callable[..., Union[None, Awaitable[None]]]
EventListener = Dict[str, List[EventCallback]]


class HubEventType(Enum):
    # Hub management events
    HUB_CREATE = 'hub_create'
    HUB_DELETE = 'hub_delete'
    HUB_UPDATE = 'hub_update'

    # User moderation events
    USER_WARN = 'user_warn'
    USER_BAN = 'user_ban'
    USER_UNBAN = 'user_unban'
    USER_MUTE = 'user_mute'
    USER_UNMUTE = 'user_unmute'

    # Server moderation events
    SERVER_WARN = 'server_warn'
    SERVER_BAN = 'server_ban'
    SERVER_UNBAN = 'server_unban'
    SERVER_MUTE = 'server_mute'
    SERVER_UNMUTE = 'server_unmute'

    # Connection events
    CONNECTION_ADD = 'connection_add'
    CONNECTION_REMOVE = 'connection_remove'

    # Message events
    MESSAGE_EDIT = 'message_edit'
    MESSAGE_DELETE = 'message_delete'
    MESSAGE_REPORT = 'message_report'
    NSFW_DETECTED = 'nsfw_detected'

    # Appeals
    APPEAL_SUBMITTED = 'appeal_submitted'


@dataclass
class HubEvent:
    """
    Represents a hub-related event with all relevant data.

    This dataclass contains all possible fields for hub events.
    Not all fields will be populated for every event type.
    """

    event_type: HubEventType
    hub_id: str
    hub_name: str
    timestamp: datetime

    # Optional fields - populated based on event type
    moderator_id: Optional[str] = None
    moderator_name: Optional[str] = None

    target_user_id: Optional[str] = None
    target_user_name: Optional[str] = None

    target_server_id: Optional[str] = None
    target_server_name: Optional[str] = None

    reason: Optional[str] = None
    duration: Optional[str] = None
    expires_at: Optional[datetime] = None

    # Report-specific fields
    report_id: Optional[str] = None

    # Appeal-specific fields
    appeal_id: Optional[str] = None

    # Message-specific fields
    message_id: Optional[str] = None
    channel_id: Optional[str] = None
    original_content: Optional[str] = None
    new_content: Optional[str] = None

    # Additional metadata
    extra_data: Optional[Dict[str, Any]] = None


class EventDispatcher:
    """
    A global event dispatcher that allows components to dispatch events.
    """

    def __init__(self):
        self._listeners: EventListener = {}
        self._global_listeners: List[EventCallback] = []
        self._is_dispatching = False
        self._event_queue: List[tuple] = []

    def listen(self, event_name: str, callback: EventCallback) -> None:
        """
        Register a callback to listen for a specific event.
        """
        if event_name not in self._listeners:
            self._listeners[event_name] = []

        self._listeners[event_name].append(callback)

    def unlisten(self, event_name: str, callback: EventCallback) -> bool:
        """
        Unregister a callback from listening to a specific event.

        Returns:
            True if the callback was found and removed, False otherwise
        """
        if event_name in self._listeners:
            try:
                self._listeners[event_name].remove(callback)

                # Clean up empty listener lists
                if not self._listeners[event_name]:
                    del self._listeners[event_name]

                return True
            except ValueError:
                pass

        return False

    def register_global_listener(self, callback: EventCallback) -> None:
        """
        Register a callback to listen for ALL events.
        """
        self._global_listeners.append(callback)
        logger.debug('Registered global event listener')

    def unregister_global_listener(self, callback: EventCallback) -> bool:
        """
        Unregister a global event listener.

        Returns:
            True if the callback was found and removed, False otherwise
        """
        try:
            self._global_listeners.remove(callback)
            logger.debug('Unregistered global event listener')
            return True
        except ValueError:
            return False

    async def dispatch(self, event_name: str, *args, **kwargs) -> None:
        """
        Dispatch an event to all registered listeners.
        """
        # If we're already dispatching, queue this event to prevent recursion
        if self._is_dispatching:
            self._event_queue.append((event_name, args, kwargs))
            return

        self._is_dispatching = True

        try:
            await self._dispatch_event(event_name, *args, **kwargs)

            # Process any queued events
            while self._event_queue:
                queued_event, queued_args, queued_kwargs = self._event_queue.pop(0)
                await self._dispatch_event(queued_event, *queued_args, **queued_kwargs)

        finally:
            self._is_dispatching = False

    async def dispatch_hub_event(self, event: HubEvent) -> None:
        """
        Dispatch a hub event using the HubEvent dataclass.
        """
        await self.dispatch(event.event_type.value, event)

    async def _dispatch_event(self, event_name: str, *args, **kwargs) -> None:
        """
        Internal method to dispatch an event to listeners.
        """

        # Collect all listeners for this event
        listeners = []

        # Add specific event listeners
        if event_name in self._listeners:
            listeners.extend(self._listeners[event_name])

        # Add global listeners
        listeners.extend(self._global_listeners)

        if not listeners:
            logger.debug(f"No listeners registered for event '{event_name}'")
            return

        # Call all listeners
        for listener in listeners:
            try:
                if asyncio.iscoroutinefunction(listener):
                    await listener(*args, **kwargs)
                else:
                    listener(*args, **kwargs)
            except Exception as e:
                logger.error(f"Error in event listener for '{event_name}': {e}", exc_info=True)

    def get_listener_count(self, event_name: Optional[str] = None) -> int:
        """
        Get the number of listeners for a specific event or all events.

        Args:
            event_name: The event name to count listeners for, or None for all
        """
        if event_name is None:
            # Count all listeners
            total = len(self._global_listeners)
            for listeners in self._listeners.values():
                total += len(listeners)
            return total

        # Count listeners for specific event
        count = len(self._global_listeners)  # Global listeners handle all events
        if event_name in self._listeners:
            count += len(self._listeners[event_name])

        return count

    def clear_listeners(self, event_name: Optional[str] = None) -> None:
        """
        Clear all listeners for a specific event or all events.

        Args:
            event_name: The event name to clear listeners for, or None for all
        """
        if event_name is None:
            self._listeners.clear()
            self._global_listeners.clear()
            logger.debug('Cleared all event listeners')
        else:
            if event_name in self._listeners:
                del self._listeners[event_name]
                logger.debug(f"Cleared listeners for event '{event_name}'")


# Global singleton dispatcher instance
event_dispatcher = EventDispatcher()


def create_hub_event(
    event_type: HubEventType, hub_id: str, hub_name: str, **kwargs: Any
) -> HubEvent:
    """
    Helper function to create a HubEvent with proper timestamp.
    """
    return HubEvent(
        event_type=event_type,
        hub_id=hub_id,
        hub_name=hub_name,
        timestamp=datetime.now(timezone.utc),
        **kwargs,
    )


# Convenience functions for common event dispatching patterns
async def dispatch_user_action(
    action_type: HubEventType,
    hub_id: str,
    hub_name: str,
    moderator_id: str,
    moderator_name: str,
    target_user_id: str,
    target_user_name: str,
    reason: Optional[str] = None,
    duration: Optional[str] = None,
    expires_at: Optional[datetime] = None,
) -> None:
    """
    Dispatch a user-related moderation event.
    """
    event = create_hub_event(
        event_type=action_type,
        hub_id=hub_id,
        hub_name=hub_name,
        moderator_id=moderator_id,
        moderator_name=moderator_name,
        target_user_id=target_user_id,
        target_user_name=target_user_name,
        reason=reason,
        duration=duration,
        expires_at=expires_at,
    )
    await event_dispatcher.dispatch_hub_event(event)


async def dispatch_server_action(
    action_type: HubEventType,
    hub_id: str,
    hub_name: str,
    moderator_id: str,
    moderator_name: str,
    target_server_id: str,
    target_server_name: str,
    reason: Optional[str] = None,
    duration: Optional[str] = None,
    expires_at: Optional[datetime] = None,
) -> None:
    """
    Dispatch a server-related moderation event.
    """
    event = create_hub_event(
        event_type=action_type,
        hub_id=hub_id,
        hub_name=hub_name,
        moderator_id=moderator_id,
        moderator_name=moderator_name,
        target_server_id=target_server_id,
        target_server_name=target_server_name,
        reason=reason,
        duration=duration,
        expires_at=expires_at,
    )
    await event_dispatcher.dispatch_hub_event(event)


async def dispatch_hub_management(
    action_type: HubEventType,
    hub_id: str,
    hub_name: str,
    moderator_id: str,
    moderator_name: str,
    reason: Optional[str] = None,
) -> None:
    """
    Dispatch a hub management event.
    """
    event = create_hub_event(
        event_type=action_type,
        hub_id=hub_id,
        hub_name=hub_name,
        moderator_id=moderator_id,
        moderator_name=moderator_name,
        reason=reason,
    )
    await event_dispatcher.dispatch_hub_event(event)


async def dispatch_connection_event(
    action_type: HubEventType,
    hub_id: str,
    hub_name: str,
    server_id: str,
    server_name: str,
    moderator_id: Optional[str] = None,
    moderator_name: Optional[str] = None,
) -> None:
    """
    Dispatch a connection-related event.
    """
    event = create_hub_event(
        event_type=action_type,
        hub_id=hub_id,
        hub_name=hub_name,
        target_server_id=server_id,
        target_server_name=server_name,
        moderator_id=moderator_id,
        moderator_name=moderator_name,
    )
    await event_dispatcher.dispatch_hub_event(event)
