import ast
import logging
import os
import sys

import colorlog
import redis.asyncio as redis_async
from dotenv import load_dotenv

load_dotenv()


def _to_bool(value: str | None, default: bool = False) -> bool:
    if value is None:
        return default
    return str(value).strip().lower() in {'1', 'true', 'yes', 'on'}


class InterchatConstants:
    def environment(self) -> str:
        return str(os.getenv('ENVIRONMENT'))

    def debug(self) -> bool:
        return _to_bool(os.getenv('DEBUG'), default=False)

    def version(self) -> str:
        version = str(os.getenv('INTERCHAT_VERSION'))
        if str(os.getenv('ENVIRONMENT')).lower() == 'development':
            return f'DEV {version}'

        return version

    def token(self) -> str:
        return str(os.getenv('DISCORD_TOKEN') or os.getenv('TOKEN') or '')

    def database_url(self) -> str:
        return str(os.getenv('DATABASE_URL'))

    def redis_uri(self) -> str:
        return str(os.getenv('REDIS_URI'))

    def prefix(self) -> str:
        return str(os.getenv('PREFIX'))

    def color(self) -> int:
        return 0x9172D8

    def support_invite(self) -> str:
        return str(os.getenv('SUPPORT_INVITE') or 'https://interchat.tech/support')

    def donate_link(self) -> str:
        return str(os.getenv('DONATE_LINK'))

    def sentry_dsn(self) -> str:
        return str(os.getenv('SENTRY_DSN'))

    def rate_limits(self) -> dict[str, dict[str, int]]:
        return {'commands': {'limit': 5, 'period': 5}, 'webhook': {'limit': 3, 'period': 300}}

    def auth_users(self) -> list[int]:
        raw = os.getenv('AUTH')
        if not raw:
            return []
        try:
            parsed = ast.literal_eval(raw)
            return [int(x) for x in parsed] if isinstance(parsed, (list, tuple)) else []
        except Exception:
            logger.warning('AUTH is not a valid list; using empty list')
            return []

    def dev_guild_id(self) -> int:
        return int(os.getenv('DEV_GUILD_ID') or 0)

    def staff_role_id(self) -> int:
        return int(os.getenv('STAFF_ROLE_ID') or 0)

    def auto_create_tables(self) -> bool:
        return _to_bool(os.getenv('AUTO_CREATE_TABLES'), default=False)

    def sentry_send_default_pii(self) -> bool:
        return _to_bool(os.getenv('SENTRY_PII'), default=False)

    def nsfw_detector_url(self) -> str:
        return str(os.getenv('NSFW_DETECTOR_URL') or '')

    def enable_nsfw_detection(self) -> bool:
        return _to_bool(os.getenv('ENABLE_NSFW'), default=True)

    def staff_report_channel_id(self) -> int:
        """Global InterChat staff reports channel ID (for platform-wide reports)."""
        return int(os.getenv('STAFF_REPORT_CHANNEL_ID') or 0)


constants = InterchatConstants()

redis_client = redis_async.from_url(
    constants.redis_uri(),
    max_connections=100,
    socket_connect_timeout=10,
    socket_timeout=10,
    retry_on_timeout=True,
    decode_responses=True,
)

# Your existing logging config
log = colorlog.ColoredFormatter(
    '%(blue)s[%(asctime)s]%(reset)s - %(filename)s - %(log_color)s%(levelname)s%(reset)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    log_colors={
        'DEBUG': 'cyan',
        'INFO': 'green',
        'WARNING': 'yellow',
        'ERROR': 'red',
        'CRITICAL': 'bold_red',
    },
)

handler = logging.StreamHandler()
handler.setFormatter(log)

logger = logging.getLogger('InterChat')
logger.addHandler(handler)
if constants.debug():
    logger.setLevel(logging.DEBUG)
else:
    logger.setLevel(logging.INFO)


# Custom exception handler
def handle_exception(exc_type, exc_value, exc_traceback):
    # Don't log KeyboardInterrupt (Ctrl+C)
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return

    # Log the exception with full traceback
    logger.critical('Uncaught exception', exc_info=(exc_type, exc_value, exc_traceback))


# Set the exception handler
sys.excepthook = handle_exception
