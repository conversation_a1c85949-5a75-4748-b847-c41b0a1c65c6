import discord
from discord.ext import commands


from sqlalchemy.dialects.postgresql import insert as pg_insert

import re
import json
import os
from datetime import datetime, timedelta
from sqlalchemy import case, select, desc
from sqlalchemy.orm import selectinload
from typing import TYPE_CHECKING, Optional, Union

from utils.modules.core.db.models import Hub, User, UserAchievement, Connection

if TYPE_CHECKING:
    from main import Bot
    from sqlalchemy.ext.asyncio import AsyncSession

discordInteraction = Union[commands.Context, discord.Interaction]


def get_source_user(source: discordInteraction):
    return source.user if isinstance(source, discord.Interaction) else source.author


def get_source_bot(source: discordInteraction):
    return source.client if isinstance(source, discord.Interaction) else source.bot


async def chunk_guilds(guilds):
    for guild in sorted(guilds, key=lambda g: g.member_count, reverse=True):
        if guild.chunked is False:
            await guild.chunk(cache=True)


def parse_discord_emoji(emoji_input: discord.Emoji | str | None) -> str:
    if not emoji_input:
        return '❓'

    def as_img(eid, name, animated):
        return f'<img src="https://cdn.discordapp.com/emojis/{eid}.{"gif" if animated else "png"}" alt="{name}" class="custom-emoji">'

    if isinstance(emoji_input, discord.Emoji):
        return as_img(emoji_input.id, emoji_input.name, getattr(emoji_input, 'animated', False))

    match = re.match(r'<(a?):([^:]+):(\d+)>', str(emoji_input))
    if match:
        animated, name, emoji_id = match.groups()
        return as_img(emoji_id, name, bool(animated))
    return '❓'


async def fetch_achievements(
    bot: 'Bot',
    user: discord.User,
    limit: int = 6,
) -> list[dict[str, str]]:
    achievement_list: list[dict[str, str]] = []

    async with bot.db.get_session() as session:
        stmt = (
            select(UserAchievement)
            .where(UserAchievement.userId == str(user.id))
            .options(selectinload(UserAchievement.achievement))
            .order_by(desc(UserAchievement.unlockedAt))
            .limit(limit)
        )
        result = (await session.execute(stmt)).scalars().all()

        for ua in result:
            achievement = ua.achievement
            if not achievement:
                continue

            data = {
                'icon': parse_discord_emoji(achievement.badgeEmoji),
                'title': achievement.name,
                'description': achievement.description,
            }
            achievement_list.append(data)

    return achievement_list


async def load_profile_data(bot: 'Bot', user: discord.User) -> Optional['User']:
    async with bot.db.get_session() as session:
        stmt = select(User).where(User.id == str(user.id))
        res = (await session.execute(stmt)).scalar_one_or_none()

    return res


async def load_user_locale(bot: 'Bot', source: discordInteraction) -> str:
    user = get_source_user(source)
    async with bot.db.get_session() as session:
        stmt = select(User.locale).where(User.id == str(user.id))
        res = (await session.execute(stmt)).scalar()
    return res or 'en'


def check_user():
    async def predicate(ctx: commands.Context['Bot']) -> bool:
        async with ctx.bot.db.get_session() as session:
            stmt = select(User.id).where(User.id == str(ctx.author.id))
            existing = (await session.execute(stmt)).scalar()

            if not existing:
                user = User(
                    id=str(ctx.author.id),
                    name=ctx.author.name,
                    image=ctx.author.display_avatar.url,
                    locale='en',
                    badges=[],
                    preferredLanguages=[],
                    lastMessageAt=datetime.now(),
                    inboxLastReadDate=datetime.now(),
                    createdAt=datetime.now(),
                    updatedAt=datetime.now(),
                )
                session.add(user)
                await session.commit()

        return True

    return commands.check(predicate)


def abbreviate_number(n):
    if n >= 1_000_000_000:
        return f'{n / 1_000_000_000:.1f}B'
    elif n >= 1_000_000:
        return f'{n / 1_000_000:.1f}M'
    elif n >= 1_000:
        return f'{n / 1_000:.1f}k'
    else:
        return str(n)


DURATION_UNITS = {
    's': 1000,
    'm': 60 * 1000,
    'h': 60 * 60 * 1000,
    'd': 24 * 60 * 60 * 1000,
    'w': 7 * 24 * 60 * 60 * 1000,
    'mo': 30 * 24 * 60 * 60 * 1000,
    'y': 12 * 30 * 24 * 60 * 60 * 1000,
}


def parse_duration(s: str):
    pattern = re.compile(r'(\d+)\s*(s|m|h|d|w|mo|y)', re.IGNORECASE)
    matches = pattern.findall(s)
    if not matches:
        raise ValueError('Invalid duration format.')

    multipliers = {
        's': 1000,
        'm': 60_000,
        'h': 3_600_000,
        'd': 86_400_000,
        'w': 604_800_000,
        'mo': 2_592_000_000,  # 30 days
        'y': 31_536_000_000,  # 365 days
    }

    total_ms = 0
    for value, unit in matches:
        total_ms += int(value) * multipliers[unit.lower()]
    return total_ms


def ms_to_datetime(duration_ms: int) -> datetime:
    return datetime.now() + timedelta(milliseconds=duration_ms)


async def upsert_user(user: discord.User | discord.Member, session: 'AsyncSession') -> User:
    """Upsert user with efficient database operations"""
    user_id = str(user.id)
    current_name = user.name
    current_avatar = user.display_avatar.url

    # Use PostgreSQL UPSERT instead of SQLAlchemy
    stmt = pg_insert(User).values(
        id=user_id,
        name=current_name,
        image=current_avatar,
        lastMessageAt=datetime.now(),
        updatedAt=datetime.now(),
    )

    # On conflict: Always update timestamps, conditionally update name/image
    stmt = stmt.on_conflict_do_update(
        index_elements=['id'],
        set_={
            'name': case((User.name != stmt.excluded.name, stmt.excluded.name), else_=User.name),
            'image': case(
                (User.image != stmt.excluded.image, stmt.excluded.image),
                else_=User.image,
            ),
            'lastMessageAt': stmt.excluded.lastMessageAt,
            'updatedAt': stmt.excluded.updatedAt,
        },
    ).returning(User)

    return (await session.execute(stmt)).scalar_one()


async def load_locales():
    with open('./data/localeMap.json', 'r', encoding='utf-8') as f:
        locale_map = json.load(f)

    abbrev_to_name = {v['code']: k for k, v in locale_map.items()}

    found_codes: set[str] = set()

    # Detect top-level locale files like locales/en.yaml, locales/fr.yaml
    for root, _, files in os.walk('./locales'):
        # Only consider the top-level for legacy files
        if os.path.abspath(root) == os.path.abspath('./locales'):
            for file in files:
                if file.endswith(('.yaml', '.yml')):
                    found_codes.add(file.split('.')[0])
        # Also consider subdirectories like locales/en/*.yml
        else:
            # The immediate directory name under locales is the language code
            lang_code = os.path.basename(root)
            for file in files:
                if file.endswith(('.yaml', '.yml')):
                    found_codes.add(lang_code)

    available_locales = []
    for lang_code in sorted(found_codes):
        if lang_code in abbrev_to_name:
            full_name = abbrev_to_name[lang_code]
            flag = locale_map[full_name].get('flag', '🌐')
            available_locales.append({'short': lang_code, 'long': full_name, 'flag': flag})
        else:
            # Fallback entry if not present in localeMap.json
            available_locales.append({'short': lang_code, 'long': lang_code, 'flag': '🌐'})

    available_locales.sort(key=lambda x: x['long'])
    return available_locales


async def get_hub(hub_id: str, session: 'AsyncSession') -> Optional[Hub]:
    """Get hub data from database."""
    stmt = select(Hub).where(Hub.id == hub_id)
    return (await session.execute(stmt)).scalar()


def ms_to_human(ms: int, max_parts=3) -> str:
    """Convert milliseconds to human-readable duration format."""
    if ms < 60_000:
        return '< 1 minute'

    UNITS = [
        ('y', 365 * 24 * 60 * 60),
        ('mo', 30 * 24 * 60 * 60),
        ('w', 7 * 24 * 60 * 60),
        ('d', 24 * 60 * 60),
        ('h', 60 * 60),
        ('m', 60),
        ('s', 1),
    ]

    total_seconds = ms // 1000
    parts = []

    for unit_name, unit_seconds in UNITS:
        if total_seconds >= unit_seconds:
            unit_count = total_seconds // unit_seconds
            parts.append(f'{int(unit_count)}{unit_name}')
            total_seconds %= unit_seconds

            if len(parts) >= max_parts:
                break

    return ' '.join(parts) if parts else '< 1 minute'

async def fetch_connections(bot: 'Bot', guild_id: str):
    async with bot.db.get_session() as session:
        stmt = (
            select(Connection, Hub.name)
            .join(Hub, Hub.id == Connection.hubId)
            .where(Connection.serverId == guild_id)
        )
        rows = (await session.execute(stmt)).all()
        return [(con, hub_name) for con, hub_name in rows]