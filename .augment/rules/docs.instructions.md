---
type: "agent_requested"
description: "When generating documentation for this project, you must:  -   Follow the **InterChat Premium Mintlify Documentation Prompt** style. -   Use a **hybrid tone** — clear, professional, but Discord-community friendly. -   Structure docs around **user goals**, not technical or alphabetical listings. -   Mention commands **only where relevant** to features."
---

# Copilot Project Instructions — InterChat Documentation

## Documentation Style Profile

When generating documentation for this project, you must:

-   Follow the **InterChat Premium Mintlify Documentation Prompt** style.
-   Use a **hybrid tone** — clear, professional, but Discord-community friendly.
-   Structure docs around **user goals**, not technical or alphabetical listings.
-   Mention commands **only where relevant** to features.

## Documentation Structure

-   **Landing Page**

    -   Banner placeholder
    -   One-paragraph value proposition
    -   Quick navigation links

-   **Getting Started Guide**

    -   Full setup walkthrough
    -   Permissions, invite link, first-run test
    -   Optional tips for smooth onboarding

-   **Feature Guides** (by user goal)

    -   Quick how-to + realistic example
    -   Visual placeholders for complex features

-   **Advanced Customization**

    -   Database tweaks
    -   Hidden settings
    -   Behavior customization

-   **Changelog**

    -   Version history with admin-relevant highlights

-   **Troubleshooting**
    -   Common issues and fixes

## Visual Integration Rules

-   More visuals for complex features, fewer for basics
-   Use placeholders like:
    -   `[IMAGE: Hub creation dialog showing server selection]`
    -   `[GIF: Message broadcasting across three connected servers]`

## Writing Style

-   Plain, accessible language
-   Short, descriptive headings
-   Realistic examples using placeholder server/channel names
-   Each feature explains:
    1. What it does
    2. How to use it
    3. Why it’s useful

## Technical Reference Process

When needed, analyze:

-   `cogs/` for user-facing commands & features
-   Database models for feature relationships
-   Moderation/validation logic for safety docs
-   Config options & customization capabilities
