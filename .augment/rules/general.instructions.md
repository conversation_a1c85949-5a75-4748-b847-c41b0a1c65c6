---
type: "always_apply"
---

# InterChat.py – Instructions

# Product Overview

InterChat is a Discord bot that enables cross-server communication through organized "hubs". It allows Discord communities to connect and communicate across multiple servers, creating bridges between communities worldwide.

## Core Functionality

-   **Hub System**: Organized channels that span multiple Discord servers
-   **Cross-Server Messaging**: Messages sent in one server appear in all connected servers within the same hub
-   **Community Building**: Connects gaming communities, study groups, creative communities, and regional groups

## Key Features

-   **Moderation**: NSFW detection, hub-level warnings/mutes/bans, block words, logging
-   **User System**: Profiles, leaderboards, achievement badges, preferences
-   **Hub Management**: Public/private hubs, staff roles (Owner/Manager/Moderator), configurable modules
-   **Internationalization**: Multi-language support

## Target Users

-   Discord server owners wanting to expand their community reach
-   Gaming communities seeking larger player bases
-   Educational groups for collaboration
-   Creative communities for sharing and feedback
-   Regional/language-based communities

## Business Model

-   Free and open source
-   Optional premium features through donations

---

## Architecture & Key Modules

## TL;DR

# Project Structure

## Root Level Organization

-   **main.py**: Bot entry point with startup logic and error handling
-   **requirements.txt**: Python dependencies
-   **pyproject.toml**: Ruff configuration (line-length: 100, single quotes)
-   **alembic.ini**: Database migration configuration
-   **docker-compose.yml**: Production container setup
-   **.env**: Environment variables (use .env.example as template)

## Core Directories

### `/cogs/` - Discord Bot Commands & Events

Organized by functionality using discord.py cog pattern:

-   **`app_commands/`**: Context Menu commands (report.py)
-   **`developer/`**: Development tools (dev_commands.py, jishaku.py)
-   **`events/`**: Event handlers (onMessage.py, onCommandError.py, etc.)
    -   **`custom/`**: Hub-specific events (connections.py, hubMessages.py, etc.)
-   **`modules/`**: Core bot features (hub.py, moderation.py, user.py, etc.)
-   **`public/`**: User-facing commands (general.py, help.py)
-   **`staff/`**: Staff-only commands (staff_commands.py)

### `/utils/` - Core Business Logic

-   **`constants.py`**: Configuration management and logging setup
-   **`utils.py`**: General utility functions
-   **`modules/`**: Organized by domain:
    -   **`core/`**: Database, caching, moderation, views
        -   **`db/`**: Database models, connection management
        -   **`views/`**: Discord UI components (buttons, modals, etc.)
        -   **`hub/`**: Hub-specific utilities
    -   **`broadcast/`**: Message broadcasting system
    -   **`emojis/`**: Emoji management and static definitions
    -   **`errors/`**: Custom error handling
    -   **`events/`**: Event system infrastructure
    -   **`services/`**: Business logic services
    -   **`ui/`**: UI components and HTML generation

### `/alembic/` - Database Migrations

-   **`versions/`**: Migration files (auto-generated)
-   **`env.py`**: Migration environment configuration

### `/locales/` - Internationalization

-   **`en/`**: English translations (commands.yml, responses.yml, ui.yml)
-   **`es/`**: Spanish translations
-   Language files in YAML format for bot responses

### `/docs/` - Documentation

-   **Mintlify-based documentation**
-   **`getting-started/`**: User onboarding guides
-   **`hub-management/`**: Hub administration docs
-   **`development/`**: Technical documentation
-   **`user-guide/`**: End-user features

### `/data/` - Data Management

-   **`badges.py`**: User achievement system
-   **`profile.py`**: User profile management
-   **`transcript.py`**: Message logging utilities

### `/scripts/` - Utility Scripts

-   **`generateEmojiClass.py`**: Emoji class generation
-   **`syncEmojis.py`**: Emoji synchronization

## Code Organization Patterns

### Cog Structure

Each cog follows this pattern:

```python
class CogName(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @commands.command()
    async def command_name(self, ctx):
        # Command logic
```

### Service Layer

Business logic is separated into service classes in `/utils/modules/services/`:

-   `hub_service.py`: Hub management operations
-   `user_service.py`: User-related operations
-   `moderation_service.py`: Moderation actions
-   `validation_service.py`: Input validation

### Database Models

SQLAlchemy models in `/utils/modules/core/db/models.py`:

-   Use dataclass pattern with `MappedAsDataclass`
-   Async session management
-   Proper indexing and relationships

### Event System

Custom event decorators in `/utils/modules/events/`:

-   `@event_handler` decorator for custom events
-   Event dispatcher for hub-specific events
-   Logging helpers for hub activities

## File Naming Conventions

-   **Snake_case**: Python files and directories
-   **PascalCase**: Class names and some specialized files
-   **camelCase**: Database column names (following Prisma conventions)
-   **kebab-case**: Documentation files

*   **Core loop:** message → validate → format → webhook fan-out → persist.
*   **Always** use shared resources (DB sessions, HTTP session), enforce validations, and re-validate webhooks before sending.
*   **No wildcard/barrel imports.** Use explicit imports; only use localized lazy imports to break a real cycle (document why).

**Entrypoint**

-   `main.py`: defines `Bot(AutoShardedBot)`, configures **PostgreSQL**, **Redis/Dragonfly**, **Sentry** (prod), and **cogwatch** hot-reload.

**Message flow (happy path)**

1. `cogs/events/onMessage.py`
2. `utils/modules/broadcast/checks.py` (validation)
3. `utils/modules/broadcast/service.py` (format + parallel sends)
4. `utils/modules/core/webhookCore.py` (create/resolve/validate webhooks)
5. Persist `Message` + `Broadcast` rows

**Data models**

-   `utils/modules/core/db/models.py`

    -   All IDs stored as **strings**.
    -   Relationships mostly `lazy='noload'`.

**Events, logging, anti-abuse**

-   Hub events/logging: `utils/modules/events/eventDispatcher.py`
-   Anti-spam: `utils/modules/core/antiSpam.py`
-   Rate limits: `utils/modules/core/rateLimit.py` (per-user commands, per-channel webhook) via Redis/Dragonfly
-   Constants/env/logger: `utils/constants.py` (includes `InterchatConstants` and `logger`)

---

## Message Validation (fast → heavy)

**File:** `utils/modules/broadcast/checks.py`

1. **Quick guards**

    - Content length ≤ 2000 chars
    - Hub lock (`hub.locked`)
    - Account age ≥ 7 days

2. **Anti-spam**
3. **Single UNION lookup**
    - Infractions / blacklists across user/guild/hub
4. **Optional NSFW screening**

    - Controlled by `ENABLE_NSFW` and `NSFW_DETECTOR_URL`

5. **Outcome**

    - Failures may notify the user and/or mute; some violations may fail silently by design

> Keep checks **pure and fast**. Return structured reasons (enum/flags) so callers can decide whether to notify or mute.

---

## Broadcasting Rules

**File:** `utils/modules/broadcast/service.py`

-   Format content + badges; attach optional reply embed/mention
-   Use **parallel** webhook sends with bounded concurrency (use `bot.http_session`; do **not** create ad-hoc sessions)
-   **Always** validate/refresh the webhook **before each** dispatch:

    -   Canonical column: `Connection.webhookURL`
    -   If missing/invalid, recreate via `webhookCore` and persist back to `Connection.webhookURL`

-   After successful sends, persist `Message` + related `Broadcast` rows

**Threads**

-   Use **parent-channel** webhooks for thread messages
-   Store thread id in `Connection.channelId` and parent id in `Connection.parentId`

---

## Webhook Lifecycle

**File:** `utils/modules/core/webhookCore.py`

-   Resolve → create (if needed) → validate → cleanup (if deleted externally)
-   Minimize Discord API calls; cache identifiers where safe
-   Respect **rate limiting** and backoff (use centralized helpers)

---

## Conventions & Coding Rules

-   **DB sessions:**

    ```py
    async with self.bot.db.get_session() as session:
        ...  # queries
        await session.commit()
    ```

    Prefer **service** modules (Hub/User/Validation) over ad-hoc queries.

-   **Imports:** no wildcard or barrel re-exports. Localized lazy imports **only** to break a verified cycle. Add a one-line comment naming the cycle.
-   **HTTP:** use shared `bot.http_session`. Do not instantiate new `ClientSession` per call.
-   **Rate limits:** use `utils/modules/core/rateLimit.py`. Don’t roll your own counters.
-   **Env/flags:** read via `utils/constants.py` (`InterchatConstants`).
-   **Logging:** use shared `logger`. Log structured context (guild/channel/user/hub IDs).

---

# Technology Stack

## Core Technologies

-   **Python 3.12**: Primary programming language
-   **discord.py**: Discord API wrapper for bot functionality
-   **PostgreSQL**: Primary database for persistent data storage
-   **Redis**: Caching and session management
-   **SQLAlchemy**: ORM for database operations with async support
-   **Alembic**: Database migration management

## Key Libraries & Frameworks

-   **asyncpg**: Async PostgreSQL driver
-   **aiohttp**: Async HTTP client for external API calls
-   **Jinja2**: Template engine for dynamic content
-   **PyYAML**: Configuration and localization file parsing
-   **Playwright**: Web automation for advanced features
-   **Sentry**: Error tracking and monitoring
-   **cogwatch**: Hot-reloading for development

## Development Tools

-   **Ruff**: Code formatting and linting (line-length: 100, single quotes)
-   **Docker**: Containerization with PostgreSQL support
-   **Alembic**: Database schema versioning

## Architecture Patterns

-   **Cog-based Architecture**: Discord.py cogs for modular command organization
-   **Service Layer Pattern**: Business logic separated into service classes
-   **Repository Pattern**: Database access abstracted through models
-   **Event-driven Architecture**: Custom event system with decorators
-   **Async/Await**: Fully asynchronous codebase

## Common Commands

### Development Setup

```bash
# Install dependencies
uv sync

# Database migrations
alembic upgrade head
alembic revision --autogenerate -m "description"

# Run bot
python main.py
```

### Docker Development

```bash
# Start PostgreSQL for development
docker-compose -f dev/docker-compose.postgres.yml up -d

# Full development environment
docker-compose -f dev/docker-compose.yml up
```

### Code Quality

```bash
# Format code
ruff format .

# Lint code
ruff check .
```

## Environment Configuration

-   Uses `.env` files for configuration
-   Environment-specific settings through `InterchatConstants` class
-   Supports development/production environment switching

---

## MCP Tooling & Operational Practices

-   **Context7 Docs (MCP):** Before upgrading dependencies or using rare API surfaces (discord.py, SQLAlchemy, aiohttp), fetch current docs. Copy relevant breaking/deprecation notes into PRs.
-   **Sentry (prod):** For runtime failures:

    1. query unresolved (last 24h), 2) open issue → inspect stack/context, 3) after fix deploy, re-query to confirm resolution.

-   **Documentation updates:** Any new validation step, service module addition/removal, moderation threshold change, or **major** dependency upgrade → update this file immediately.

**Pre-merge checklist (significant changes)**

-   [ ] Fetched latest docs via Context7 for affected APIs
-   [ ] Searched Sentry for related open issues/regressions
-   [ ] Added/updated validations and tests where applicable
-   [ ] Updated this instructions file if a new rule/pattern emerged

---

## Gotchas & Edge Cases

-   **IDs are strings.** Avoid int/str mismatches—normalize early.
-   **Webhooks disappear.** Validate/recreate **every** broadcast and persist to `Connection.webhookURL`.
-   **Concurrency.** Bound all broadcast fan-outs; never unbounded `gather`.
-   **Hub infractions.** Track `notified` to avoid duplicate DMs.
-   **Localization.** YAML in `locales/`; embeds via `utils/modules/ui/CreateEmbed.py`.

---

## Start Here (files to open first)

1. `cogs/events/onMessage.py`
2. `utils/modules/broadcast/service.py`
3. `utils/modules/broadcast/checks.py`
4. `utils/modules/core/webhookCore.py`
5. `utils/modules/core/db/models.py`
6. `utils/constants.py`

---

## Integration Snapshot

-   discord.py 2.x, SQLAlchemy (async), aiohttp, Redis/Dragonfly, Sentry (prod)
-   Hub events/logging: `utils/modules/events/`
-   Custom errors: `utils/modules/errors/customDiscord.py`
-   Centralized handler: `utils/modules/errors/errorHandler.py`

---

## Style & Safety Hints

-   Prefer **explicit** imports and **service functions** over inline SQLAlchemy queries in handlers.
-   Return **typed results**/enums from validators; keep Discord I/O out of pure logic.
-   Use **context managers** for DB sessions and transactions; commit once per unit of work.
-   When touching webhooks, call `webhookCore.validate_or_create(...)` (or equivalent helper) and **persist** updates to `Connection.webhookURL`.
-   Don’t spin new event loops, sessions, or executors; reuse bot resources.

## Before handing over to user

-   Ensure all new features and changes are documented in `/docs/`. They are user-facing docs and guides. NOT technical docs.
-   Analyze and index documentation in .github/instructions/docs.instructions.md, keep it up to date
-   Regularly review and update the documentation and documentation instructions to reflect any changes in the codebase or features
-   Refactor to improve code quality, readability, and maintainability
-   Ensure all code follows the project's coding standards and best practices
