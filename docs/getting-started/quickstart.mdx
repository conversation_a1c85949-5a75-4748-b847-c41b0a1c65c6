---
title: "InterChat Quickstart"
description: "Quick, clear, and addresses privacy + security concerns."
icon: "rocket"
---

## TL;DR (2 min)

1. Invite the bot with the _minimal_ permissions listed below.
2. Run `/setup` in a channel you control.
3. Create or join a **hub** and invite other servers.
4. Lock it down with filters, mods, and hub settings.

---

## Invite the bot

Use the invite link from the dashboard (or your deployment). When adding the bot, **only** grant the permissions it needs:

-   Send Messages
-   Manage Messages (optional, for cleanup)
-   Create Webhooks (required for cross-server relay)
-   Use Slash Commands
-   Read Message History (only for channels it's active in)

**Don't** give `Administrator` unless you _really_ trust the instance.

> Tip: create a dedicated bot role and grant that role the permissions above instead of giving it server-wide admin.

---

## Quick setup (run in Discord)

1. In the channel you want InterChat to operate in, type `/setup`.
2. Follow the interactive prompts:
    - Choose to Create a Hub or Join a Hub.
    - If joining, either pick a public hub from the directory or paste an invite code you received from a hub owner.
    - The bot will guide you and confirm when your channel is connected.
3. Done! Your channel is now linked to the hub and will relay messages to other connected channels.

---

## Common commands (cheat sheet)

```
/setup                          — guided setup menu
/hubs                           — list public hubs
/hub create                     — create a new hub (Owner)
/hub configure                  — configure a hub (Owner/Manager)
/hub invites                    — view/create invite codes for your hub
/connect hub:<name> [channel]   — connect this channel to a public hub
/connect invite:<code> [channel]— connect using an invite code
/disconnect                     — remove this channel from its hub

/infractions hub:<hub> [user|server]   — view infractions (Moderator+)
/mod panel [user|message|server]       — open moderation panel (Moderator+)
/mod warn|mute|ban hub:<hub> [...]     — take action (Moderator+)
/appeal                         — submit an appeal (users)

/profile [@user]  /leaderboard [filter]  /help  /about  /invite  /stats
```

Tip: Use Discord’s tab-complete to see the exact parameter names available in your server.

---

## Privacy & data 

People worry about "what data gets shared" and “who can read my messages”. here's how to think about it and what to do.

-   **What gets forwarded:** messages posted in a channel that you explicitly connect to a hub are forwarded to every other channel in that hub. If you don't connect a channel, nothing is forwarded.
-   **Who must consent:** every server admin (or the person running `/setup`) must explicitly opt in. Hubs are not stealthy. Don't be added to a hub without approval.
-   **Personally-identifiable info (PII):** messages may contain usernames or content. if you need to avoid exposing PII, don't post it in hub-linked channels.
-   **Storage & logs:** the bot may store message metadata and logs for moderation, analytics, or delivery reliability depending on deployment. self-hosting gives you full control over retention.
-   **Telemetry:** Official hosted instances may collect anonymous telemetry. If this matters, self-host or check the hosted instance's privacy docs and opt-out options.

**How to reduce risk**

-   Use **private hubs** (invite-only).
-   Enable and configure **word filters** and **NSFW detection**.
-   Limit which channels are connected, don't hook up admin or mod channels.
-   Encourage users to not post DMs, passwords, tokens, or sensitive files in hub channels.

---

## Moderation & safety

InterChat includes moderation tools. make use of them:

-   **Word filters:** block slurs, doxxing patterns, or other sensitive strings.
-   **NSFW detection:** flag or block adult content from being relayed.
-   **Infraction system:** warn/timeout/ban repeat offenders.
-   **Channel whitelist:** only allow certain roles to post to the hub channel.

If a bridged message violates rules, moderators can delete the message locally; the bot can be configured to remove the message across the hub.

---

## Troubleshooting

-   **Bot not responding:** check bot role & permissions in that channel and confirm the token is valid.
-   **Messages not relaying:** make sure the channel is connected to a hub and the hub has other active channels.
-   **NSFW false positives/negatives:** tune thresholds or disable auto-block if it's blocking legit content.
-   **Permission errors on delete/manage:** bot needs `Manage Messages` to delete relay copies.

---

## FAQ

**Q: Can InterChat read channels I don't connect?**
A: No. It only interacts with channels you explicitly configure.

**Q: Can it DM users?**
A: Usually no — most hubs relay messages in channels only. DMs should be avoided for hub content.

**Q: Who owns the data?**
A: If you use the official hosted instance, data is stored/processed by the host (check their privacy docs). If you self-host, you own the data.
