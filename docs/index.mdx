---
title: "Welcome to InterChat"
description: "Connect Discord servers across the globe with InterChat's cross-server communication platform"
icon: "house"
---

<Frame>
<img src="/images/hero-light.png" alt="InterChat connecting Discord servers worldwide" />
</Frame>

## What is InterChat?

InterChat is a powerful Discord bot that **bridges servers together**, allowing communities to communicate across Discord servers through organized "hubs." Think of it as creating channels that span multiple servers, bringing together communities from around the world.

## How it Works

<Steps>
<Step title="Add InterChat to Your Server">
  Invite InterChat and set up a channel for cross-server communication.
</Step>

<Step title="Join a Hub">
  Browse and connect to hubs that match your community's interests.
</Step>

<Step title="Start Chatting">
  Your messages now reach members across all servers connected to that hub!
</Step>
</Steps>

## Popular Use Cases

<AccordionGroup>
<Accordion title="Gaming Communities">
Connect gaming servers for specific games, creating larger communities for players to find teammates, share strategies, and discuss updates.
</Accordion>

<Accordion title="Study Groups">
Link educational servers for specific subjects or courses, allowing students worldwide to collaborate and help each other.
</Accordion>

<Accordion title="Creative Communities">
Bridge art, music, or writing servers to share work, get feedback, and collaborate on projects.
</Accordion>

<Accordion title="Regional Communities">
Connect servers based on geographic location or language for local discussions and meetups.
</Accordion>
</AccordionGroup>

## Key Features

### 🔒 Moderation
- NSFW image detection (when enabled by the host)
- Hub-level warnings, mutes, and bans
- Optional block words and invite blocking (per hub modules)
- Logging to staff channels

### 👥 User Features
- User profiles and a leaderboard
- Achievement badges
- Preferences: language, badge visibility, reply mentions

### 🛠️ Hub Management
- Create hubs; make them public or invite-only
- Configure modules, logging, and general settings
- Manage staff roles (Owner, Manager, Moderator)

### 🌍 Internationalization
- Multi-language responses where translations exist

## Getting Started

Ready to connect your community? Choose your path:

<CardGroup cols={2}>
<Card 
  title="🚀 Quick Start" 
  icon="rocket"
  href="/getting-started/quickstart"
>
  Get up and running in 5 minutes
</Card>

<Card 
  title="⚙️ Detailed Setup" 
  icon="gear"
  href="/getting-started/basic-setup"
>
  Complete configuration guide
</Card>

<Card 
  title="🏗️ Create a Hub" 
  icon="hammer"
  href="/hub-management/creating-hubs"
>
  Start your own community hub
</Card>

<Card 
  title="🤖 For Developers" 
  icon="code"
  href="/development/introduction"
>
  API docs and development guides
</Card>
</CardGroup>

## Community & Support

<Info>
Need help? Our community is here to assist you!
</Info>

<CardGroup cols={3}>
<Card title="Discord Server" icon="discord" href="https://www.interchat.tech/support">
  Join our support server for help and updates
</Card>

<Card title="Documentation" icon="book" href="/getting-started/quickstart">
  Comprehensive guides and tutorials
</Card>

<Card title="GitHub" icon="github" href="https://github.com/interchatapp/InterChat.py">
  Open source code and issue tracking
</Card>
</CardGroup>

---

<Note>
InterChat is completely **free to use** and **open source**. We're committed to building bridges between communities and fostering global connections.
</Note>
