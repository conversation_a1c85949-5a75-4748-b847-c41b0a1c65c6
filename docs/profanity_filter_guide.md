# Comprehensive Profanity Filter System

## Overview

The InterChat profanity filter system provides comprehensive content moderation with advanced pattern matching, flexible actions, and robust bypass mechanisms. It's designed for high performance and easy administration.

## Features

### 🔧 Pattern Matching Engine
- **Exact Match** (`word`): Blocks only that word (case-insensitive, word boundaries)
- **Prefix Match** (`word*`): Blocks any word starting with the prefix
- **Suffix Match** (`*word`): Blocks any word ending with the suffix
- **Wildcard Match** (`*word*`): Blocks any word containing the substring
- Case-insensitive matching with Unicode normalization
- Word boundary detection to prevent false positives
- Precompiled patterns for optimal performance

### ⚖️ Moderation Actions
- **Block Message**: Prevent message from being sent
- **Warning System**: Issue warnings with escalation thresholds
- **Mute**: Temporary mute with configurable duration
- **Ban**: Permanent or temporary ban from hub
- **Alert**: Send notifications to moderators
- **Action Chaining**: Execute multiple actions simultaneously

### 🔑 Bypass System
- **Role-based exemptions**: Hub owners, managers, moderators
- **Discord role exemptions**: Configurable role-based bypass
- **Temporary bypass tokens**: Time-limited, use-limited tokens
- **Word whitelist**: Per-hub safe words that are never blocked

### 📊 Analytics & Audit
- **Comprehensive logging**: All violations and actions tracked
- **Pattern effectiveness**: Track trigger counts and false positives
- **Trend analysis**: Violation patterns over time
- **User violation summaries**: Individual user statistics
- **Performance metrics**: System performance monitoring

## Quick Start

### Basic Setup

```python
from utils.modules.core.profanity import ProfanityFilterService
from sqlalchemy.orm import Session

# Initialize with database session
filter_service = ProfanityFilterService(db_session)

# Check a message
result = await filter_service.check_message(
    message="Some message content",
    hub_id="hub_123",
    user_id="user_456",
    channel_id="channel_789"
)

if result.blocked:
    print(f"Message blocked: {result.matches}")
    print(f"Actions taken: {result.actions_executed}")
```

### Creating Rules and Patterns

```python
from utils.modules.core.db.models import AntiSwearRule, AntiSwearPattern, PatternMatchType, BlockWordAction

# Create a new rule
rule = AntiSwearRule(
    hubId="hub_123",
    name="Basic Profanity Filter",
    createdBy="admin_user",
    enabled=True,
    actions=[BlockWordAction.BLOCK_MESSAGE, BlockWordAction.WARN],
    warningThreshold=3,
    escalationAction=BlockWordAction.MUTE,
    muteDurationMinutes=60,
    logViolations=True
)

# Add patterns to the rule
patterns = [
    AntiSwearPattern(ruleId=rule.id, pattern="badword", matchType=PatternMatchType.EXACT),
    AntiSwearPattern(ruleId=rule.id, pattern="spam", matchType=PatternMatchType.PREFIX),
    AntiSwearPattern(ruleId=rule.id, pattern="ing", matchType=PatternMatchType.SUFFIX),
    AntiSwearPattern(ruleId=rule.id, pattern="bad", matchType=PatternMatchType.WILDCARD)
]
```

## Pattern Types

### Exact Match
```
Pattern: "badword"
Matches: "badword"
Doesn't match: "badwords", "mybadword", "bad word"
```

### Prefix Match
```
Pattern: "spam*" (entered as "spam*")
Matches: "spam", "spammer", "spamming"
Doesn't match: "antispam", "spam bot" (two words)
```

### Suffix Match
```
Pattern: "*ing" (entered as "*ing")
Matches: "running", "jumping", "thing"
Doesn't match: "ingredient", "ing", "ing word"
```

### Wildcard Match
```
Pattern: "*bad*" (entered as "*bad*")
Matches: "badword", "mybadword", "embadded"
Doesn't match: "ba d", "b ad" (broken by spaces)
```

## Admin Tools

### Bulk Pattern Import

```python
from utils.modules.core.profanity.admin_tools import BulkPatternManager

manager = BulkPatternManager(db_session)

# Parse patterns from text input
patterns = manager.parse_input_text("""
badword1, badword2*
*suffix1, *wildcard*
exact_word
""")

# Import to a rule
result = manager.import_patterns_to_rule(
    rule_id="rule_123",
    patterns=patterns,
    validate=True,
    skip_duplicates=True
)

print(f"Imported: {result.imported_patterns}")
print(f"Skipped: {result.skipped_patterns}")
print(f"Errors: {result.errors}")
```

### Pattern Validation

```python
from utils.modules.core.profanity.admin_tools import PatternValidator

validator = PatternValidator()

result = validator.validate_pattern("test", PatternMatchType.EXACT)
if result.valid:
    print("Pattern is valid")
else:
    print(f"Errors: {result.errors}")
    print(f"Warnings: {result.warnings}")
```

### Configuration Export/Import

```python
from utils.modules.core.profanity.admin_tools import ConfigurationManager

config_manager = ConfigurationManager(db_session)

# Export hub configuration
config = config_manager.export_hub_config("hub_123")

# Copy configuration between hubs
success = config_manager.copy_config_between_hubs(
    source_hub_id="hub_123",
    target_hub_id="hub_456",
    created_by="admin_user",
    overwrite=True
)
```

## Bypass System

### Role-based Bypass

```python
from utils.modules.core.profanity.bypass import BypassConfig

# Configure bypass rules
bypass_config = BypassConfig(
    exempt_roles={"moderator_role_id", "trusted_role_id"},
    exempt_moderators=True,
    exempt_managers=True,
    exempt_owners=True
)

# Check with bypass configuration
result = await filter_service.check_message(
    message="message content",
    hub_id="hub_123",
    user_id="user_456",
    channel_id="channel_789"
)
```

### Temporary Bypass Tokens

```python
from utils.modules.core.profanity.bypass import BypassChecker

bypass_checker = BypassChecker(db_session)

# Create a bypass token
token = await bypass_checker.create_bypass_token(
    hub_id="hub_123",
    user_id="user_456",
    created_by="admin_user",
    reason="Temporary bypass for testing",
    duration_minutes=60,
    uses=5
)

# User can now use: [bypass:TOKEN] in their message
```

### Whitelist Management

```python
from utils.modules.core.db.models import AntiSwearWhitelist

# Add words to whitelist
whitelist_entry = AntiSwearWhitelist(
    hubId="hub_123",
    word="class",  # Won't be blocked even if patterns match
    createdBy="admin_user",
    reason="Common word that causes false positives"
)
```

## Analytics and Reporting

### Violation Statistics

```python
from utils.modules.core.profanity.analytics import ProfanityAnalytics

analytics = ProfanityAnalytics(db_session)

# Get comprehensive stats
stats = analytics.get_violation_stats("hub_123")
print(f"Total violations: {stats.total_violations}")
print(f"Unique users: {stats.unique_users}")
print(f"Top words: {stats.top_triggered_words}")

# Get pattern effectiveness
effectiveness = analytics.get_pattern_effectiveness("hub_123")
for pattern in effectiveness[:5]:  # Top 5
    print(f"Pattern: {pattern.pattern} - Score: {pattern.effectiveness_score}")

# Generate full report
report = analytics.generate_report("hub_123", days=30)
```

### Trend Analysis

```python
# Get trend data
trends = analytics.get_trend_data("hub_123", days=7, granularity="daily")
for trend in trends:
    print(f"{trend.period}: {trend.violations} violations")
```

## Performance Considerations

### Pattern Optimization
- Use exact matches when possible (fastest)
- Avoid overly broad wildcard patterns
- Regularly review pattern effectiveness
- Remove unused or ineffective patterns

### Caching
- Rules are cached per hub for 5 minutes
- Force reload with `filter_service.reload_rules(hub_id)`
- Pattern compilation is done once at load time

### Database Indexes
The system includes optimized database indexes for:
- Hub-based queries
- User violation lookups
- Pattern trigger tracking
- Audit log searches

## Best Practices

### Pattern Design
1. **Start with exact matches** for known problematic words
2. **Use prefixes/suffixes carefully** to avoid false positives
3. **Test patterns thoroughly** before deploying
4. **Maintain a whitelist** for common false positives
5. **Regular review** of triggered patterns

### Action Configuration
1. **Start with warnings** before escalating to mutes/bans
2. **Set reasonable thresholds** for escalation
3. **Configure appropriate durations** for temporary actions
4. **Enable logging** for audit trails
5. **Test action chains** in a controlled environment

### Bypass Management
1. **Limit bypass tokens** to specific use cases
2. **Set short expiration times** for tokens
3. **Regular cleanup** of expired tokens
4. **Document bypass reasons** for audit purposes
5. **Review bypass usage** regularly

## Troubleshooting

### Common Issues

**Patterns not matching:**
- Check pattern type (exact vs prefix vs suffix vs wildcard)
- Verify word boundaries are working correctly
- Check if word is whitelisted
- Ensure rule is enabled

**False positives:**
- Add problematic words to whitelist
- Refine pattern specificity
- Consider using exact matches instead of wildcards
- Review pattern effectiveness analytics

**Performance issues:**
- Reduce number of wildcard patterns
- Optimize pattern compilation
- Check database query performance
- Review cache hit rates

**Actions not executing:**
- Verify rule configuration
- Check user bypass status
- Review action executor logs
- Ensure database permissions

### Debug Mode

```python
# Enable detailed logging
import logging
logging.getLogger('profanity_filter').setLevel(logging.DEBUG)

# Check specific pattern matching
matcher = PatternMatcher()
matcher.add_pattern("test", PatternMatchType.EXACT)
matches = matcher.check_message("test message")
print(f"Matches: {matches}")
```

## API Reference

See the individual module documentation for detailed API reference:
- `utils.modules.core.profanity.pattern_matcher`
- `utils.modules.core.profanity.filter_service`
- `utils.modules.core.profanity.actions`
- `utils.modules.core.profanity.bypass`
- `utils.modules.core.profanity.admin_tools`
- `utils.modules.core.profanity.analytics`
